<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Scheduler job for code preflighting on trunk-content-dev.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>0</maxConcurrentPerNode>
            <maxConcurrentTotal>1</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>unshelve_changelist</name>
                    <description>Specifies code changelist to preflight</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>preflighter</name>
                    <description>Specifies who triggers the preflight.</description>
                    <defaultValue>anonymous</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>false</string>
                            <string>true</string>
                        </a>
                    </choices>
                    <name>sync_to_head</name>
                    <description>Set to true to ignore any lastknowngood changelist and tell perforce to sync to #head.</description>
                </hudson.model.ChoiceParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>P4CL</name>
                    <description>when enable_custom_changelist is true, this has highest priority to overwrite other settings</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>false</string>
                            <string>true</string>
                        </a>
                    </choices>
                    <name>clean_local</name>
                    <description>If true, TnT/Local will be deleted at the beginning of the run.</description>
                </hudson.model.ChoiceParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>not</string>
                            <string>do</string>
                        </a>
                    </choices>
                    <name>only_warm_machine</name>
                    <description>If set to "do", only run code_changelist to warm up machine, not care unshelve_changelist.</description>
                </hudson.model.ChoiceParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>force_rebuild</name>
                    <defaultValue>true</defaultValue>
                    <description>Forces a rebuild and does not take previous build results into account</description>
                </hudson.model.BooleanParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>branch_name=trunk-content-dev
codepreflight_reference_job=trunk-content-dev.code.lastknowngood
datapreflight_reference_job=trunk-content-dev.data.lastknowngood
project_name=bct
project_short_name=bct
retry_limit=1
use_last_known_good_code_cl=false
enable_custom_changelist=true</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def project = ProjectClass(env.project_name)

/**
 * code_preflight_scheduler.groovy
 */
pipeline {
    agent { label 'scheduler' }  // we only wanna job to get Running with 'Maximum Total Concurrent Builds', scheduler == master
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger code preflight jobs') {
            steps {
                script {
                    List&lt;JobReference&gt; jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        def cleanLocal = params.clean_local

                        // priority: input custom CL (if enable) &gt; LKG CL from job&gt; 'head' &gt; ''
                        def lkgCodeChangelist = LibJenkins.getLastStableCodeChangelist(env.codepreflight_reference_job)
                        def lkgDataChangelist = LibJenkins.getLastStableDataChangelist(env.datapreflight_reference_job)
                        def syncChangeslist = ''
                        if (params.sync_to_head.toString() == 'true') {
                            syncChangeslist = 'now'
                        }

                        boolean enableCustomChangelist = env.enable_custom_changelist?.toBoolean()
                        def codeChangelist = enableCustomChangelist ? (params.P4CL ?: lkgCodeChangelist ?: syncChangeslist) : (lkgCodeChangelist ?: syncChangeslist)
                        def dataChangelist = enableCustomChangelist &amp;&amp; params.P4CL &amp;&amp; project.frostbite_syncer_setup ? codeChangelist : lkgDataChangelist ?: syncChangeslist

                        // warmer job use code_changelist as unshelve_changeslist if set to "do"
                        def unshelveChangelist = params.only_warm_machine == 'do' ? codeChangelist : params.unshelve_changelist

                        // this is what we pass down to downstream job
                        def args = [
                            string(name: 'unshelve_changelist', value: unshelveChangelist),
                            string(name: 'code_changelist', value: codeChangelist),
                            string(name: 'data_changelist', value: dataChangelist),
                            string(name: 'clean_local', value: cleanLocal),
                            string(name: 'only_warm_machine', value: params.only_warm_machine),
                        ]
                        def injectMap = [
                            'code_changelist'    : codeChangelist,
                            'data_changelist'    : dataChangelist,
                            'unshelve_changelist': unshelveChangelist,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = env.JOB_NAME + '.' + unshelveChangelist + '.' + params.preflighter

                        def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
                        def branchInfo = branchfile.general_settings + branchfile.standard_jobs_settings + branchfile.preflight_settings
                        def codePreflightMatrix = branchfile.code_preflight_matrix

                        if (branchInfo.trigger_ado) {
                            LibCommonCps.triggerAdoCodePreflights(this, branchfile, params.preflighter, unshelveChangelist, codeChangelist)
                        }

                        def jobs = [:]
                        for (platform in codePreflightMatrix) {
                            for (config in platform.configs) {
                                String separator = platform.nomaster_platform?.toBoolean() ? '.nomaster.' : '.'
                                def onPremJobName = "${env.branch_name}.code.preflight.${platform.name}${separator}${config}"
                                def cloudJobName = "${env.branch_name}.code.preflight.${platform.name}${separator}${config}.cloud"
                                def jobName = onPremJobName
                                List&lt;String&gt; labels = [env.branch_name, 'code', platform.name]
                                if (branchInfo.enable_hybrid_agents &amp;&amp; !LibJenkins.onPremAgentsWithLabelsAvailable(labels)) {
                                   //jobName = cloudJobName
                                    echo 'Hybrid preflights are disabled. Putting job on onprem queue.'
                                }
                                labels += platform.nomaster_platform ? ['code-nomaster'] : []
                                if (params.force_rebuild || NeedsRebuildCodePreflight(onPremJobName, unshelveChangelist, 20) &amp;&amp;
                                    NeedsRebuildCodePreflight(cloudJobName, unshelveChangelist, 20)) {
                                    jobs[jobName] = {
                                        def downstreamJob = build(job: jobName, parameters: args, propagate: false)
                                        jobReferences &lt;&lt; new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: args, propagate: false)
                                        LibJenkins.printRunningJobs(this)
                                    }
                                }
                            }
                        }
                        parallel(jobs)
                    }
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, true) } }
    }
    post {
        always {
            emailext(
                to: '<EMAIL>',
                subject: 'preflight result',
                body: '${SCRIPT, template="email-pipeline-preflight.groovy"}',
                mimeType: 'text/html',
                presendScript: '${SCRIPT, template="preflight-email-presend-pipeline.groovy"}'
            )
        }
        failure {
            sendAlertIfFailedConsecutiveTimes env.JOB_NAME, env.JOB_URL, env.BUILD_NUMBER, '#cobra-red-pod-alerts', env.project_short_name, 5
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>