package com.ea.project

import com.ea.project.bct.branchsettings.Bf_media
import com.ea.project.bct.branchsettings.Bf_playtest_sp
import com.ea.project.bct.branchsettings.Criterion_content_dev
import com.ea.project.bct.branchsettings.Dev_na_to_trunk
import com.ea.project.bct.branchsettings.Dev_na_to_trunk_sub
import com.ea.project.bct.branchsettings.EcsSplines
import com.ea.project.bct.branchsettings.Task1
import com.ea.project.bct.branchsettings.Task3
import com.ea.project.bct.branchsettings.Trunk_code_dev
import com.ea.project.bct.branchsettings.Trunk_code_dev_clean
import com.ea.project.bct.branchsettings.Trunk_code_dev_sanitizers
import com.ea.project.bct.branchsettings.Trunk_code_dev_test
import com.ea.project.bct.branchsettings.Trunk_content_dev
import com.ea.project.bct.branchsettings.Trunk_content_dev_cache
import com.ea.project.bct.branchsettings.Trunk_content_dev_metrics
import com.ea.project.bct.branchsettings.Trunk_content_dev_test
import com.ea.project.bct.branchsettings.Trunk_to_dev_na
import com.ea.project.bctch1.branchsettings.Bf_anticheat
import com.ea.project.bctch1.branchsettings.CH1_2024_to_CH1
import com.ea.project.bctch1.branchsettings.CH1_SP_content_dev
import com.ea.project.bctch1.branchsettings.CH1_SP_content_dev_disc_build
import com.ea.project.bctch1.branchsettings.CH1_SP_content_dev_first_patch
import com.ea.project.bctch1.branchsettings.CH1_SP_release
import com.ea.project.bctch1.branchsettings.CH1_SP_stage
import com.ea.project.bctch1.branchsettings.CH1_bflabs_qol
import com.ea.project.bctch1.branchsettings.CH1_bflabs_release
import com.ea.project.bctch1.branchsettings.CH1_bflabs_stage
import com.ea.project.bctch1.branchsettings.CH1_code_dev
import com.ea.project.bctch1.branchsettings.CH1_content_dev
import com.ea.project.bctch1.branchsettings.CH1_content_dev_C1S2B1
import com.ea.project.bctch1.branchsettings.CH1_content_dev_TOP
import com.ea.project.bctch1.branchsettings.CH1_content_dev_cache_losangeles
import com.ea.project.bctch1.branchsettings.CH1_content_dev_clean
import com.ea.project.bctch1.branchsettings.CH1_content_dev_disc_build
import com.ea.project.bctch1.branchsettings.CH1_content_dev_first_patch
import com.ea.project.bctch1.branchsettings.CH1_content_dev_metrics
import com.ea.project.bctch1.branchsettings.CH1_content_dev_sanitizers
import com.ea.project.bctch1.branchsettings.CH1_event
import com.ea.project.bctch1.branchsettings.CH1_event_release
import com.ea.project.bctch1.branchsettings.CH1_hotfix
import com.ea.project.bctch1.branchsettings.CH1_marketing_dev
import com.ea.project.bctch1.branchsettings.CH1_playtest_gnt
import com.ea.project.bctch1.branchsettings.CH1_playtest_gnt_na
import com.ea.project.bctch1.branchsettings.CH1_playtest_san
import com.ea.project.bctch1.branchsettings.CH1_playtest_san_s2
import com.ea.project.bctch1.branchsettings.CH1_playtest_sp
import com.ea.project.bctch1.branchsettings.CH1_playtest_stage
import com.ea.project.bctch1.branchsettings.CH1_qol
import com.ea.project.bctch1.branchsettings.CH1_release
import com.ea.project.bctch1.branchsettings.CH1_release_playtest_gnt_na
import com.ea.project.bctch1.branchsettings.CH1_stage
import com.ea.project.bctch1.branchsettings.CH1_stage_playtest_sp
import com.ea.project.bctch1.branchsettings.CH1_to_trunk
import com.ea.project.bctch1.branchsettings.Task13
import com.ea.project.bctch1.branchsettings.Task2
import com.ea.project.bctch1.branchsettings.Task4
import com.ea.project.bctch1.branchsettings.Task8
import com.ea.project.fb1.branchsettings.DevBfPatchStream
import com.ea.project.fb1.branchsettings.DevNaBattlefieldGame
import com.ea.project.fb1.branchsettings.DevNaBattlefieldGameAsan
import com.ea.project.fb1.branchsettings.DevNaBattlefieldGameFirstPatch
import com.ea.project.gnt.branchsettings.Gnt_proto
import com.ea.project.kin.branchsettings.AWS_kin_dev_unverified
import com.ea.project.kin.branchsettings.AWS_stage_kin_dev_unverified
import com.ea.project.kin.branchsettings.Dre_triggering
import com.ea.project.kin.branchsettings.Future_dev_content
import com.ea.project.kin.branchsettings.Future_dev_runmode
import com.ea.project.kin.branchsettings.Future_dev_runmode_02
import com.ea.project.kin.branchsettings.Kin_dev
import com.ea.project.kin.branchsettings.Kin_dev_anticheat
import com.ea.project.kin.branchsettings.Kin_dev_unverified
import com.ea.project.kin.branchsettings.Kin_dev_unverified_outsource_code
import com.ea.project.kin.branchsettings.Kin_live
import com.ea.project.kin.branchsettings.Kin_live_server
import com.ea.project.kin.branchsettings.Kin_mkt_art
import com.ea.project.kin.branchsettings.Kin_playtest
import com.ea.project.kin.branchsettings.Kin_release
import com.ea.project.kin.branchsettings.Kin_soaks
import com.ea.project.kin.branchsettings.Kin_stage
import com.ea.project.kin.branchsettings.Testenv
import com.ea.project.kin.branchsettings.Upgrade_kin

class GetBranchFile {
    static Class get_branchfile(def project_name, def branch_name) {
        def branchName = branch_name.toLowerCase()
        def error = {
            throw new IllegalArgumentException("GetBranchFile was called with invalid branch name: $branchName")
        }

        switch (project_name.toLowerCase()) {
            case 'bct': switch (branchName) {
                case 'trunk-code-dev': return Trunk_code_dev
                case 'trunk-content-dev': return Trunk_content_dev
                case 'trunk-to-dev-na': return Trunk_to_dev_na
                case 'dev-na-to-trunk': return Dev_na_to_trunk
                case 'dev-na-to-trunk-sub': return Dev_na_to_trunk_sub
                case 'ecs-splines': return EcsSplines
                case 'task1': return Task1
                case 'task3': return Task3
                case 'bf-playtest-sp': return Bf_playtest_sp
                case 'media-team': return Bf_media
                case 'criterion-content-warm': return Criterion_content_dev
                case 'trunk-content-dev-test': return Trunk_content_dev_test
                case 'trunk-content-dev-cache': return Trunk_content_dev_cache
                case 'trunk-content-dev-metrics': return Trunk_content_dev_metrics
                case 'trunk-code-dev-test': return Trunk_code_dev_test
                case 'trunk-code-dev-clean': return Trunk_code_dev_clean
                case 'trunk-code-dev-sanitizers': return Trunk_code_dev_sanitizers
                default: error()
            }

            case 'bctch1': switch (branchName) {
                case '2024_1_dev-bf-to-ch1': return CH1_2024_to_CH1
                case 'bf-anticheat': return Bf_anticheat
                case 'ch1-code-dev': return CH1_code_dev
                case 'ch1-content-dev-clean': return CH1_content_dev_clean
                case 'ch1-content-dev-sanitizers': return CH1_content_dev_sanitizers
                case 'ch1-content-dev': return CH1_content_dev
                case 'ch1-content-dev-cache-losangeles': return CH1_content_dev_cache_losangeles
                case 'ch1-content-dev-disc-build': return CH1_content_dev_disc_build
                case 'ch1-content-dev-first-patch': return CH1_content_dev_first_patch
                case 'ch1-content-dev-metrics': return CH1_content_dev_metrics
                case 'ch1-content-dev-c1s2b1': return CH1_content_dev_C1S2B1
                case 'ch1-content-dev-top': return CH1_content_dev_TOP
                case 'ch1-sp-content-dev': return CH1_SP_content_dev
                case 'ch1-sp-stage': return CH1_SP_stage
                case 'ch1-sp-content-dev-disc-build': return CH1_SP_content_dev_disc_build
                case 'ch1-sp-content-dev-first-patch': return CH1_SP_content_dev_first_patch
                case 'ch1-to-trunk': return CH1_to_trunk
                case 'ch1-bflabs-stage': return CH1_bflabs_stage
                case 'ch1-bflabs-release': return CH1_bflabs_release
                case 'ch1-bflabs-qol': return CH1_bflabs_qol
                case 'ch1-hotfix': return CH1_hotfix
                case 'ch1-marketing-dev': return CH1_marketing_dev
                case 'ch1-playtest-gnt': return CH1_playtest_gnt
                case 'ch1-playtest-gnt-na': return CH1_playtest_gnt_na
                case 'ch1-playtest-san': return CH1_playtest_san
                case 'ch1-playtest-san-s2': return CH1_playtest_san_s2
                case 'ch1-playtest-sp': return CH1_playtest_sp
                case 'ch1-playtest-stage': return CH1_playtest_stage
                case 'ch1-release': return CH1_release
                case 'ch1-release-playtest-gnt-na': return CH1_release_playtest_gnt_na
                case 'ch1-event-release': return CH1_event_release
                case 'ch1-event': return CH1_event
                case 'ch1-sp-release': return CH1_SP_release
                case 'ch1-stage': return CH1_stage
                case 'ch1-stage-playtest-sp': return CH1_stage_playtest_sp
                case 'ch1-qol': return CH1_qol
                case 'ecs-splines': return EcsSplines
                case 'task13': return Task13
                case 'task2': return Task2
                case 'task4': return Task4
                case 'task8': return Task8
                default: error()
            }

            case 'fb1': switch (branchName) {
                case '2019_x': return DevNaBattlefieldGame
                case 'dev-na': return DevNaBattlefieldGame
                default: error()
            }

            case 'fb1-battlefieldgame': switch (branchName) {
                case 'dev-na-battlefieldgame': return DevNaBattlefieldGame
                case 'dev-na-battlefieldgame-asan': return DevNaBattlefieldGameAsan
                case 'dev-na-battlefieldgame-first-patch': return DevNaBattlefieldGameFirstPatch
                case '2024_1_dev-bf': return DevBfPatchStream
                default: error()
            }

            case 'granite': switch (branchName) {
                case 'gnt-proto': return Gnt_proto
                default: error()
            }

            case 'kingston': switch (branchName) {
                case 'dre-triggering': return Dre_triggering
                case 'future-dev-content': return Future_dev_content
                case 'future-dev-runmode': return Future_dev_runmode
                case 'future-dev-runmode-02': return Future_dev_runmode_02
                case 'jenkins-upgrade-kin': return Upgrade_kin
                case 'kin-dev': return Kin_dev
                case 'kin-dev-anticheat': return Kin_dev_anticheat
                case 'kin-dev-unverified': return Kin_dev_unverified
                case 'kin-dev-unverified-outsource-code': return Kin_dev_unverified_outsource_code
                case 'kin-live': return Kin_live
                case 'kin-live-server': return Kin_live_server
                case 'kin-mkt-art': return Kin_mkt_art
                case 'kin-playtest': return Kin_playtest
                case 'kin-release': return Kin_release
                case 'kin-soaks': return Kin_soaks
                case 'kin-stage': return Kin_stage
                case 'lab.kin-dev': return Testenv
                default: error()
            }

            case 'kingston_aws': switch (branchName) {
                case 'kin-dev-unverified': return AWS_kin_dev_unverified
                case 'stage-kin-dev-unverified': return AWS_stage_kin_dev_unverified
                default: error()
            }

            case 'merlin': switch (branchName) {
                case 'build-main': return com.ea.project.mer.branchsettings.BuildMain
                default: error()
            }

            case 'nfsupgrade': switch (branchName) {
                case 'upgrade': return com.ea.project.nfs.branchsettings.Upgrade
                default: error()
            }

            case 'test': switch (branchName) {
                case 'lab.kin-dev': return Kin_dev
                default: error()
            }

            default: throw new IllegalArgumentException('GetBranchFile called with invalid project name: ' + project_name)
        }
    }
}
