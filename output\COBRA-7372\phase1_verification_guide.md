# Phase 1 Verification Guide - Separate Combine Bundles Rollout

## Overview
This guide outlines the verification steps for Phase 1 of the separate combine bundles job rollout for COBRA-7372.

## Phase 1 Changes Made

### Streams Updated
1. **CH1-content-dev-disc-build**
   - File: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_content_dev_disc_build.groovy`
   - Added separate combine bundles configuration for win64 platform only

2. **CH1-content-dev-first-patch**
   - File: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_content_dev_first_patch.groovy`
   - Added separate combine bundles configuration for win64 platform only

### Configuration Added
```groovy
// COBRA-7372: Enable separate combined bundles jobs (Phase 1 - win64 only)
use_separate_combined_job    : true,
combined_job_platforms       : ['win64'],
combined_job_label_type      : 'poolbuild',
```

## Verification Steps

### 1. Jenkins Job Generation
After deploying the configuration changes:

1. **Check for new Jenkins jobs**:
   - `CH1-content-dev-disc-build.combined_bundles.win64`
   - `CH1-content-dev-first-patch.combined_bundles.win64`

2. **Verify job configuration**:
   - Job label should be: `poolbuild && win64`
   - Job should have proper parameters for combined bundles creation
   - Timeout and concurrency settings should be applied

### 2. Job Execution Verification

#### For CH1-content-dev-disc-build:
1. **Trigger a build** on the stream
2. **Monitor scheduler execution**:
   - Verify that `combined_bundles.win64` job runs before `frosty_for_patch` jobs
   - Check that the job completes successfully
3. **Check bundle creation**:
   - Verify bundles are created in the expected location
   - Confirm bundle files are properly named and structured

#### For CH1-content-dev-first-patch:
1. **Trigger a build** on the stream
2. **Monitor scheduler execution**:
   - Verify that `combined_bundles.win64` job runs before `patchfrosty` jobs
   - Check that the job completes successfully
   - Verify `first_patch` flag is properly handled
3. **Check bundle creation**:
   - Verify bundles are created in the expected location
   - Confirm delta bundles are created (due to patchfrosty matrix)

### 3. File Share Verification

#### Expected Bundle Locations
Check that combined bundles are created in the correct file share locations:

1. **For CH1-content-dev-disc-build**:
   - Path pattern: `{build_share}/CH1-content-dev-disc-build/{changelist}/combined_bundles/win64/`
   - Files should include combined bundle archives

2. **For CH1-content-dev-first-patch**:
   - Path pattern: `{build_share}/CH1-content-dev-first-patch/{changelist}/combined_bundles/win64/`
   - Files should include combined bundle archives and delta bundles

#### File Share Access
- Verify that the bundles are accessible from the expected network paths
- Check file permissions and sizes are reasonable
- Confirm bundle integrity (no corruption)

### 4. Frosty/Patchfrosty Job Integration

#### CH1-content-dev-disc-build (frosty_for_patch):
1. **Monitor frosty_for_patch jobs** that use combine format
2. **Verify parameter passing**:
   - Check that `--use-precreated-combined-bundles` flag is used
   - Confirm combine changelist parameters are passed correctly
3. **Check bundle consumption**:
   - Verify that frosty jobs can find and use the pre-created bundles
   - Monitor for any path or access issues

#### CH1-content-dev-first-patch (patchfrosty):
1. **Monitor patchfrosty jobs** that use combine format
2. **Verify parameter passing**:
   - Check that `--use-precreated-combined-bundles` flag is used
   - Confirm combine changelist parameters are passed correctly
   - Verify `first_patch` handling
3. **Check bundle consumption**:
   - Verify that patchfrosty jobs can find and use the pre-created bundles
   - Confirm delta bundles are properly utilized

### 5. Performance and Resource Monitoring

#### Machine Utilization
- **Monitor poolbuild machine usage** during combined bundles creation
- **Check for resource contention** with other poolbuild jobs
- **Verify job scheduling** doesn't cause bottlenecks

#### Build Times
- **Compare total build times** before and after the change
- **Monitor combined bundles job duration** (should be reasonable)
- **Check for any performance regressions** in downstream jobs

### 6. Error Handling and Rollback

#### Common Issues to Watch For
1. **Bundle creation failures**:
   - Check logs for avalanche.combine errors
   - Verify input bundle availability
   - Monitor disk space on build machines

2. **Path resolution issues**:
   - Verify bundle paths are correctly constructed
   - Check for UNC path access problems
   - Monitor file share connectivity

3. **Parameter passing problems**:
   - Verify changelist parameters are correctly passed
   - Check for missing or incorrect combine arguments
   - Monitor scheduler parameter propagation

#### Rollback Procedure
If issues are encountered:
1. **Disable the feature** by setting `use_separate_combined_job: false`
2. **Redeploy configuration** to revert to inline bundle creation
3. **Monitor recovery** of affected builds
4. **Document issues** for resolution before next phase

## Success Criteria

### Phase 1 is considered successful when:
1. ✅ New combined bundles jobs are created and execute successfully
2. ✅ Bundles are produced correctly and stored in expected file share locations
3. ✅ Frosty/patchfrosty jobs can consume the pre-created bundles without issues
4. ✅ No performance regressions or resource contention issues
5. ✅ Build success rates remain stable or improve
6. ✅ No critical errors or failures related to the new functionality

### Metrics to Track
- **Job success rate**: Combined bundles jobs should have >95% success rate
- **Build time impact**: Total build time should not increase significantly
- **Resource utilization**: Poolbuild machines should not be overloaded
- **Error frequency**: No increase in build failures related to bundle handling

## Next Steps After Verification
Once Phase 1 verification is complete and successful:
1. Proceed to Phase 2: Expand to all platforms (ps5, xbsx)
2. Document any lessons learned or configuration adjustments needed
3. Plan Phase 3 rollout to additional streams
4. Begin planning for dedicated machine requirements (Phase 4)

## Monitoring Commands

### Check Jenkins Jobs
```bash
# List new combined bundles jobs
curl -s "http://jenkins-server/api/json" | jq '.jobs[] | select(.name | contains("combined_bundles"))'
```

### Monitor Build Logs
```bash
# Check combined bundles job logs
curl -s "http://jenkins-server/job/CH1-content-dev-disc-build.combined_bundles.win64/lastBuild/consoleText"
```

### Verify File Share Contents
```powershell
# Check bundle creation on file share
Get-ChildItem "\\build-share\path\to\bundles" -Recurse | Where-Object {$_.Name -like "*combined*"}
```
