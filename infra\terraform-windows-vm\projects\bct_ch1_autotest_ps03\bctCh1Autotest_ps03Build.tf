#*************************************************************
#  Sets up the initial needs to point to our vSphere server
#*************************************************************
# Point to our datacentre
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# BCT: bct-x, check CONTRIBUTING.md before editing here.
# Labels bct_statebuild and bct_poolbuild are used for bct when we have
# vms with bct code server and bct data server
# *************************************************************

locals {
  module_settings = {
    # STATEBUILD NODES
    "bct_ch1_ps03_pool_state_bct_ch1_win64_002" = { datastore = "BPS03-A1_DRE-BUILD-VMS-02", vm_count = "7", labels = "ps03 amd win64_02 statebuild poolbuild win64" }
    "bct_ch1_ps03_pool_state_bct_ch1_ps5_001"   = { datastore = "BPS03-A1_DRE-BUILD-VMS-01", vm_count = "5", labels = "ps03 amd ps5_01 statebuild poolbuild ps5" }
    "bct_ch1_ps03_pool_state_bct_ch1_ps5_002"   = { datastore = "BPS03-A1_DRE-BUILD-VMS-02", vm_count = "8", labels = "ps03 amd ps5_02 statebuild poolbuild ps5" }
    "bct_ch1_ps03_pool_state_bct_ch1_ps5_003"   = { datastore = "BPS03-A1_DRE-BUILD-VMS-03", vm_count = "6", labels = "ps03 amd ps5_03 statebuild poolbuild ps5" }
    "bct_ch1_ps03_pool_state_bct_ch1_xbsx_001"  = { datastore = "BPS03-A1_DRE-BUILD-VMS-01", vm_count = "6", labels = "ps03 amd xbsx_01 statebuild poolbuild xbsx" }
    "bct_ch1_ps03_pool_state_bct_ch1_xbsx_003"  = { datastore = "BPS03-A1_DRE-BUILD-VMS-03", vm_count = "4", labels = "ps03 amd xbsx_03 statebuild poolbuild xbsx" }
    "bct_ch1_ps03_pool_state_bct_ch1_xbsx_004"  = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "7", labels = "ps03 amd xbsx_04 statebuild poolbuild xbsx" }

    # Additional VMs on BPS03-A1_DRE-BUILD-VMS-04 datastore
    "bct_ch1_ps03_pool_state_bct_ch1_win64_004"   = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "3", labels = "ps03 amd win64_04 statebuild poolbuild win64" }
    "bct_ch1_ps03_pool_state_bct_ch1_ps5_004"     = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "1", labels = "ps03 amd ps5_04 statebuild poolbuild ps5" }
    "bct_ch1_ps03_pool_state_bct_ch1_xbsx_005"    = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "1", labels = "ps03 amd xbsx_05 statebuild poolbuild xbsx" }
    "bct_ch1_ps03_pool_state_bct_ch1_linux64_001" = { datastore = "BPS03-A1_DRE-BUILD-VMS-01", vm_count = "2", labels = "ps03 amd statebuild poolbuild linux64" }
    "bct_ch1_ps03_pool_state_bct_ch1_linux64_002" = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "2", labels = "ps03 amd statebuild poolbuild linux64" }
    "bct_ch1_ps03_pool_state_bct_ch1_servers_001" = { datastore = "BPS03-A1_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps03 amd statebuild poolbuild server linuxserver" }

    # LKGAUTO NODES
    "bct_ch1_ps03_lkgauto_xbsx_001"  = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "1", labels = "ps03 amd lkg_auto xbsx" }
    "bct_ch1_ps03_lkgauto_ps5_001"   = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "1", labels = "ps03 amd lkg_auto ps5" }
    "bct_ch1_ps03_lkgauto_win64_001" = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "1", labels = "ps03 amd lkg_auto win64" }

    # TEST NODE
    "bct_ch1_ps03_testnode_win64_001" = { datastore = "BPS03-A1_DRE-BUILD-VMS-04", vm_count = "1", labels = "testnode" }
  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "bct2-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://bct-ch1-autotest-jenkins.cobra.dre.ea.com")
  vsphere_compute_cluster = try(each.value.compute_cluster, "DICE-BUILD-STUDIO-AMD-01")
  ram_count               = try(each.value.ram_count, 65536)
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")

  vsphere_template      = "win10_22H2-cobra-v1.1101.0b76e848_PS03"
  vsphere_network       = "Buildfarm (VLAN 1028)"
  vsphere_datacenter    = "DICE"
  vsphere_folder        = "DICE/dre-terraform-nodes/bct_nodes"
  domain_admin          = var.domain_admin
  domain_admin_password = var.domain_password
  local_admin_user      = var.local_username
  local_admin_password  = var.local_password
  project_dir           = var.project_dir
  project_name          = "bct_ch1_autotest_ps03"
  commit_sha            = var.commit_sha
  commit_user           = var.commit_user
  commit_url            = var.commit_url
  disk_size             = "700"
  domain_name           = "dice.ad.ea.com"
  domain_ou             = "OU=BuildMonkeys,OU=Computers,OU=Stockholm,OU=Offices,DC=dice,DC=ad,DC=ea,DC=com"
  hardware_version      = var.hardware_version
  local_admin_group     = "AD\\BCT.SECURITY.BUILD.AGENTS"
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
