# Phase 5: Dedicated Machine Configuration Templates

## Overview
This document provides the exact configuration changes needed for Phase 5 rollout to dedicated machine streams.

## Configuration Templates

### 1. CH1-content-dev Configuration
**File**: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_content_dev.groovy`

```groovy
// BEFORE (around line 139):
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-content-dev.deployment-data.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-content-dev',
    source_branch_data: 'CH1-SP-content-dev',
],

// AFTER:
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-content-dev.deployment-data.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-content-dev',
    source_branch_data: 'CH1-SP-content-dev',
    
    // COBRA-7372: Enable separate combined bundles jobs (Phase 5 - dedicated machines)
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
],
```

### 2. CH1-release Configuration
**File**: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_release.groovy`

```groovy
// BEFORE (around line 79):
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-release.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-release',
    source_branch_data: 'CH1-SP-release',
],

// AFTER:
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-release.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-release',
    source_branch_data: 'CH1-SP-release',
    
    // COBRA-7372: Enable separate combined bundles jobs (Phase 5 - dedicated machines)
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
],
```

### 3. CH1-stage Configuration
**File**: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_stage.groovy`

```groovy
// BEFORE (around line 71):
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-stage.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-stage',
    source_branch_data: 'CH1-SP-stage',
],

// AFTER:
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-stage.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-stage',
    source_branch_data: 'CH1-SP-stage',
    
    // COBRA-7372: Enable separate combined bundles jobs (Phase 5 - dedicated machines)
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
],
```

### 4. CH1-qol Configuration
**File**: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_qol.groovy`

```groovy
// BEFORE (around line 69):
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-release.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-release',
    source_branch_data: 'CH1-SP-release',
],

// AFTER:
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-release.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-release',
    source_branch_data: 'CH1-SP-release',
    
    // COBRA-7372: Enable separate combined bundles jobs (Phase 5 - dedicated machines)
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
],
```

### 5. CH1-bflabs-release Configuration
**File**: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_bflabs_release.groovy`

```groovy
// BEFORE (around line 67):
combine_bundles: [
    combine_asset: 'Game/Setup/Build/LabsClientLevels',
    combine_reference_job: 'CH1-SP-release.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-release',
    source_branch_data: 'CH1-SP-release',
],

// AFTER:
combine_bundles: [
    combine_asset: 'Game/Setup/Build/LabsClientLevels',
    combine_reference_job: 'CH1-SP-release.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-release',
    source_branch_data: 'CH1-SP-release',
    
    // COBRA-7372: Enable separate combined bundles jobs (Phase 5 - dedicated machines)
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
],
```

### 6. CH1-bflabs-stage Configuration
**File**: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_bflabs_stage.groovy`

```groovy
// BEFORE (around line 61):
combine_bundles: [
    combine_asset: 'Game/Setup/Build/LabsClientLevels',
    combine_reference_job: 'CH1-SP-stage.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-stage',
    source_branch_data: 'CH1-SP-stage',
],

// AFTER:
combine_bundles: [
    combine_asset: 'Game/Setup/Build/LabsClientLevels',
    combine_reference_job: 'CH1-SP-stage.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-stage',
    source_branch_data: 'CH1-SP-stage',
    
    // COBRA-7372: Enable separate combined bundles jobs (Phase 5 - dedicated machines)
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
],
```

### 7. CH1-bflabs-qol Configuration
**File**: `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_bflabs_qol.groovy`

```groovy
// BEFORE (around line 67):
combine_bundles: [
    combine_asset: 'Game/Setup/Build/LabsClientLevels',
    combine_reference_job: 'CH1-SP-release.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-release',
    source_branch_data: 'CH1-SP-release',
],

// AFTER:
combine_bundles: [
    combine_asset: 'Game/Setup/Build/LabsClientLevels',
    combine_reference_job: 'CH1-SP-release.patchdata.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-release',
    source_branch_data: 'CH1-SP-release',
    
    // COBRA-7372: Enable separate combined bundles jobs (Phase 5 - dedicated machines)
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
],
```

## Implementation Script

### Automated Configuration Update Script
```powershell
# PowerShell script to apply all Phase 5 configurations
# Run from: C:\Users\<USER>\vscode

$streams = @(
    @{
        file = "pycharm\dst-ci-configuration\src\com\ea\project\bctch1\branchsettings\CH1_content_dev.groovy"
        search = "combine_bundles                    : \["
        lineAfter = "            source_branch_data   : 'CH1-SP-content-dev',"
    },
    @{
        file = "pycharm\dst-ci-configuration\src\com\ea\project\bctch1\branchsettings\CH1_release.groovy"
        search = "combine_bundles           : \["
        lineAfter = "            source_branch_data   : 'CH1-SP-release',"
    },
    @{
        file = "pycharm\dst-ci-configuration\src\com\ea\project\bctch1\branchsettings\CH1_stage.groovy"
        search = "combine_bundles               : \["
        lineAfter = "            source_branch_data   : 'CH1-SP-stage',"
    },
    @{
        file = "pycharm\dst-ci-configuration\src\com\ea\project\bctch1\branchsettings\CH1_qol.groovy"
        search = "combine_bundles           : \["
        lineAfter = "            source_branch_data   : 'CH1-SP-release',"
    },
    @{
        file = "pycharm\dst-ci-configuration\src\com\ea\project\bctch1\branchsettings\CH1_bflabs_release.groovy"
        search = "combine_bundles             : \["
        lineAfter = "            source_branch_data   : 'CH1-SP-release',"
    },
    @{
        file = "pycharm\dst-ci-configuration\src\com\ea\project\bctch1\branchsettings\CH1_bflabs_stage.groovy"
        search = "combine_bundles             : \["
        lineAfter = "            source_branch_data   : 'CH1-SP-stage',"
    },
    @{
        file = "pycharm\dst-ci-configuration\src\com\ea\project\bctch1\branchsettings\CH1_bflabs_qol.groovy"
        search = "combine_bundles             : \["
        lineAfter = "            source_branch_data   : 'CH1-SP-release',"
    }
)

$configToAdd = @"
            
            // COBRA-7372: Enable separate combined bundles jobs (Phase 5 - dedicated machines)
            use_separate_combined_job    : true,
            combined_job_platforms       : ['win64', 'ps5', 'xbsx'],
            combined_job_label_type      : 'dedicated',
"@

foreach ($stream in $streams) {
    Write-Host "Updating $($stream.file)..."
    
    $content = Get-Content $stream.file
    $newContent = @()
    
    foreach ($line in $content) {
        $newContent += $line
        if ($line.Trim() -eq $stream.lineAfter.Trim()) {
            $newContent += $configToAdd
        }
    }
    
    Set-Content -Path $stream.file -Value $newContent
    Write-Host "Updated $($stream.file)"
}

Write-Host "All Phase 5 configurations applied successfully!"
```

## Expected Jenkins Jobs After Phase 5

### New Jobs Created (21 total):
1. `CH1-content-dev.combined_bundles.win64`
2. `CH1-content-dev.combined_bundles.ps5`
3. `CH1-content-dev.combined_bundles.xbsx`
4. `CH1-release.combined_bundles.win64`
5. `CH1-release.combined_bundles.ps5`
6. `CH1-release.combined_bundles.xbsx`
7. `CH1-stage.combined_bundles.win64`
8. `CH1-stage.combined_bundles.ps5`
9. `CH1-stage.combined_bundles.xbsx`
10. `CH1-qol.combined_bundles.win64`
11. `CH1-qol.combined_bundles.ps5`
12. `CH1-qol.combined_bundles.xbsx`
13. `CH1-bflabs-release.combined_bundles.win64`
14. `CH1-bflabs-release.combined_bundles.ps5`
15. `CH1-bflabs-release.combined_bundles.xbsx`
16. `CH1-bflabs-stage.combined_bundles.win64`
17. `CH1-bflabs-stage.combined_bundles.ps5`
18. `CH1-bflabs-stage.combined_bundles.xbsx`
19. `CH1-bflabs-qol.combined_bundles.win64`
20. `CH1-bflabs-qol.combined_bundles.ps5`
21. `CH1-bflabs-qol.combined_bundles.xbsx`

## Prerequisites for Phase 5

### Infrastructure Requirements
- ✅ Dedicated machine requirements document completed
- 📋 Infrastructure team approval and machine provisioning
- 📋 Machine setup and configuration
- 📋 Jenkins node registration
- 📋 Network and access validation

### Testing Requirements
- 📋 Machine connectivity tests
- 📋 Build tool validation
- 📋 Performance benchmarking
- 📋 End-to-end job execution tests

## Rollout Strategy

### Recommended Order
1. **CH1-content-dev** (highest activity, good for testing)
2. **CH1-release** (production critical)
3. **CH1-stage** (production critical)
4. **CH1-qol** (medium priority)
5. **CH1-bflabs-release** (medium priority)
6. **CH1-bflabs-stage** (medium priority)
7. **CH1-bflabs-qol** (lowest priority)

### Validation Steps per Stream
1. Apply configuration changes
2. Deploy to Jenkins
3. Verify job creation
4. Test job execution
5. Monitor performance
6. Validate bundle creation and consumption
7. Move to next stream

## Rollback Plan

### Quick Rollback
If issues are encountered, disable the feature by changing:
```groovy
use_separate_combined_job: false,
```

### Full Rollback
1. Revert all configuration changes
2. Redeploy to Jenkins
3. Verify inline bundle creation works
4. Monitor build recovery
5. Document issues for future resolution
