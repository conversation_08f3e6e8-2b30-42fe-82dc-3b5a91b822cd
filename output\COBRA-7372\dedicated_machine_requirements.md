# Dedicated Machine Requirements for Separate Combine Bundles Jobs

## Overview
This document outlines the infrastructure requirements for implementing separate combine bundles jobs on dedicated machines for BCT CH1 streams.

## Machine Requirements Summary

### Total Machine Labels Required
- **Number of Streams**: 7 streams requiring dedicated machines
- **Platforms per Stream**: 3 platforms (win64, ps5, xbsx)
- **Total Machine Labels**: 21 dedicated machine labels

### Machine Label Naming Convention
- Format: `{stream_name} combine-bundles {platform}`
- Example: `CH1-content-dev combine-bundles win64`

## Detailed Requirements by Stream

### 1. CH1-content-dev
- **Machine Labels**:
  - `CH1-content-dev combine-bundles win64`
  - `CH1-content-dev combine-bundles ps5`
  - `CH1-content-dev combine-bundles xbsx`
- **Priority**: High (active development stream)
- **Special Requirements**: None

### 2. CH1-release
- **Machine Labels**:
  - `CH1-release combine-bundles win64`
  - `CH1-release combine-bundles ps5`
  - `CH1-release combine-bundles xbsx`
- **Priority**: High (production stream)
- **Special Requirements**: None

### 3. CH1-stage
- **Machine Labels**:
  - `CH1-stage combine-bundles win64`
  - `CH1-stage combine-bundles ps5`
  - `CH1-stage combine-bundles xbsx`
- **Priority**: High (production stream)
- **Special Requirements**: None

### 4. CH1-qol
- **Machine Labels**:
  - `CH1-qol combine-bundles win64`
  - `CH1-qol combine-bundles ps5`
  - `CH1-qol combine-bundles xbsx`
- **Priority**: Medium
- **Special Requirements**: None

### 5. CH1-bflabs-release
- **Machine Labels**:
  - `CH1-bflabs-release combine-bundles win64`
  - `CH1-bflabs-release combine-bundles ps5`
  - `CH1-bflabs-release combine-bundles xbsx`
- **Priority**: Medium
- **Special Requirements**: 
  - EAC enabled
  - Custom combine settings files

### 6. CH1-bflabs-stage
- **Machine Labels**:
  - `CH1-bflabs-stage combine-bundles win64`
  - `CH1-bflabs-stage combine-bundles ps5`
  - `CH1-bflabs-stage combine-bundles xbsx`
- **Priority**: Medium
- **Special Requirements**: None

### 7. CH1-bflabs-qol
- **Machine Labels**:
  - `CH1-bflabs-qol combine-bundles win64`
  - `CH1-bflabs-qol combine-bundles ps5`
  - `CH1-bflabs-qol combine-bundles xbsx`
- **Priority**: Low
- **Special Requirements**: None

## Hardware Specifications

### Recommended Machine Specifications
- **CPU**: 8+ cores (similar to existing frosty build machines)
- **RAM**: 32GB+ (combine operations are memory-intensive)
- **Disk**: 500GB+ SSD (fast I/O for bundle operations)
- **Network**: 10Gbps (high bandwidth for file transfers)

### Concurrency Requirements
- **Per Stream**: Maximum 3 concurrent jobs (one per platform)
- **Total Concurrent Jobs**: Up to 21 if all streams build simultaneously
- **Realistic Concurrency**: 5-10 concurrent jobs (based on typical build patterns)

## Software Requirements

### Required Software
- **Operating System**: Windows Server 2019+
- **Build Tools**:
  - Elipy environment
  - Avalanche tools
  - Frostbite SDK
  - P4 client
  - Jenkins agent

### Environment Configuration
- **Environment Variables**:
  - PATH including all required tools
  - Proper authentication for file shares
  - P4 configuration

## Network and Access Requirements

### Network Access
- **File Shares**:
  - DiceStockholm build share (`\\filer.dice.ad.ea.com\builds\Battlefield`)
  - Guildford build share (`\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds`)
  - Other regional build shares as needed
- **P4 Servers**:
  - DICE P4 server (`dice-p4buildedge02-fb.dice.ad.ea.com:2001`)
  - LA P4 server (`dicela-p4edge-fb.la.ad.ea.com:2001`)
  - Other regional P4 servers as needed
- **Jenkins**:
  - Connection to Jenkins master
  - Agent port access

### Authentication
- **Service Accounts**:
  - P4 service account with read access
  - File share access credentials
  - Jenkins agent authentication

## Resource Utilization

### Estimated Resource Usage
- **CPU Usage**: 50-80% during bundle creation
- **Memory Usage**: 16-24GB during peak operations
- **Disk I/O**: High during bundle extraction/creation
- **Network I/O**: 2-5Gbps during bundle transfers

### Job Duration
- **Average Job Duration**: 30-60 minutes
- **Peak Resource Usage**: 15-30 minutes during bundle creation
- **Idle Time**: Variable based on build schedule

## Implementation Strategy

### Phased Rollout Approach
1. **Initial Setup** (2-3 machines):
   - Create machines for CH1-content-dev (highest priority)
   - Test and validate setup
   - Monitor performance and resource usage

2. **Production Streams** (6-9 machines):
   - Add machines for CH1-release and CH1-stage
   - Validate production workloads
   - Adjust specifications if needed

3. **Remaining Streams** (12+ machines):
   - Complete rollout to all remaining streams
   - Optimize resource allocation based on usage patterns

### Machine Provisioning Options
1. **Dedicated Physical Machines**:
   - Pros: Consistent performance, no resource contention
   - Cons: Higher cost, less flexibility

2. **Virtual Machines**:
   - Pros: Better resource utilization, easier scaling
   - Cons: Potential performance variability

3. **Hybrid Approach** (Recommended):
   - Physical machines for high-priority streams
   - Virtual machines for lower-priority streams
   - Dynamic scaling based on demand

## Monitoring and Maintenance

### Monitoring Requirements
- **Performance Metrics**:
  - CPU, memory, disk, and network utilization
  - Job duration and success rate
  - Queue time and wait periods
- **Alerting**:
  - Resource exhaustion alerts
  - Job failure notifications
  - Infrastructure issues

### Maintenance Procedures
- **Regular Updates**:
  - OS patches and security updates
  - Build tool updates
  - Jenkins agent updates
- **Scaling Procedures**:
  - Process for adding new machines
  - Load balancing across machines
  - Decommissioning unused resources

## Cost Estimation

### Infrastructure Costs
- **Hardware Costs**: Dependent on physical vs. virtual approach
- **Software Licensing**: Minimal (mostly using existing licenses)
- **Operational Costs**: Additional monitoring and maintenance

### Cost-Benefit Analysis
- **Benefits**:
  - Reduced build times
  - Improved resource utilization
  - Better isolation between streams
  - Enhanced build reliability
- **ROI Factors**:
  - Developer productivity gains
  - Reduced build failures
  - Faster release cycles

## Next Steps

### Immediate Actions
1. **Infrastructure Request**:
   - Submit formal request for machine provisioning
   - Include specifications and requirements
   - Prioritize by stream importance

2. **Configuration Preparation**:
   - Prepare Jenkins node configurations
   - Create setup scripts for new machines
   - Document machine setup procedures

3. **Testing Plan**:
   - Develop validation tests for new machines
   - Create performance benchmarks
   - Establish monitoring baselines

### Timeline
- **Request Submission**: Immediate
- **Initial Machine Setup**: 1-2 weeks
- **First Stream Migration**: 2-3 weeks
- **Complete Rollout**: 4-8 weeks (depending on infrastructure availability)

## Appendix: Configuration Examples

### Jenkins Node Configuration Example
```xml
<slave>
  <name>CH1-content-dev combine-bundles win64</name>
  <description>Dedicated machine for CH1-content-dev combined bundles creation (win64)</description>
  <remoteFS>C:\jenkins</remoteFS>
  <numExecutors>1</numExecutors>
  <mode>EXCLUSIVE</mode>
  <label>CH1-content-dev combine-bundles win64</label>
  <launcher class="hudson.slaves.JNLPLauncher">
    <workDirSettings>
      <disabled>false</disabled>
      <internalDir>remoting</internalDir>
      <failIfWorkDirIsMissing>false</failIfWorkDirIsMissing>
    </workDirSettings>
  </launcher>
  <retentionStrategy class="hudson.slaves.RetentionStrategy$Always"/>
</slave>
```

### Branch Settings Configuration Example
```groovy
combine_bundles: [
    combine_asset: 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-content-dev.deployment-data.start',
    is_target_branch: true,
    source_branch_code: 'CH1-SP-content-dev',
    source_branch_data: 'CH1-SP-content-dev',
    
    // Enable separate combined bundles jobs with dedicated machines
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
]
```
