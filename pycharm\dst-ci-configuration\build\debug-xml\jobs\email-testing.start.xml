<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Test sending an email after a finished job.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>unshelve_changelist</name>
                    <description>Changelist to unshelve.</description>
                    <defaultValue>1234</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>preflighter</name>
                    <description>Name of the user triggering the job.</description>
                    <defaultValue>DICE\bvaksdal</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>true</string>
                            <string>false</string>
                        </a>
                    </choices>
                    <name>success_report</name>
                    <description>If true, send an email with success report. If false, send an email with failure report.</description>
                </hudson.model.ChoiceParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>branch_name=dre-triggering
project_name=kingston</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.testjobs

import com.ea.lib.LibJenkins
import com.sonyericsson.jenkins.plugins.bfa.BuildFailureScanner
import org.jenkinsci.plugins.workflow.cps.CpsThreadGroup

/**
 * email_testing_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        // here we need clone git repo, because we are calling PreflightEmailSending() which needs code in workspace
        stage('Sync DRE Git repository') {
            steps {
                GitSync('dst-preflights')
            }
        }
        stage('Trigger email testing jobs') {
            steps {
                script {
                    def args = []
                    def jobs = [:]

                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER

                    def jobs_to_report = [
                        'job1',
                        'job2',
                    ]

                    if (params.success_report == 'false') {
                        jobs_to_report = jobs_to_report + 'job3' + 'job4'
                    }

                    def final_result = Result.SUCCESS

                    for (job_to_report in jobs_to_report) {
                        def job_name = 'email-testing.' + job_to_report
                        jobs[job_name] = {
                            def downstream_job = build(job: job_name, parameters: args, propagate: false)
                            final_result = final_result.combine(Result.fromString(downstream_job.result))
                            LibJenkins.printRunningJobs(this)
                        }
                    }

                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
        stage('Scan for errors') {
            steps {
                script {
                    BuildFailureScanner.scanIfNotScanned(currentBuild.rawBuild, CpsThreadGroup.current().execution.owner.listener.logger)
                    currentBuild.rawBuild.save()
                }
            }
        }
    }
    post {
        always {
            PreflightEmailSending()
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>