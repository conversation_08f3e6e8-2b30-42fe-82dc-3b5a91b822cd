<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>20</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

import hudson.console.ModelHyperlinkNote

/**
* createTestCloudAgents.groovy
* Create test cloud agents for debugging
* Agent creation happens automatically by just calling a label
*/

final String CREDENTIALS_URL = 'https://ess.ea.com/ui/vault/secrets/secrets%2Fkv/show/cobra/automation/azure/dre-bf-frosted-azure/local-admin-test?namespace=cds-dre-prod'

pipeline {
    parameters {
        choice(
            name: 'stream',
            choices: ['kin-dev-unverified','dev-na-battlefieldgame'],
            description: 'Select the stream that you want the VM created from'
        )
    }
    options { timestamps() }
    agent { label "${params.stream}-test &amp;&amp; cloud" }
    stages {
        stage('Create') {
            steps {
                script {
                    Node agentNode = Jenkins.get().getNode(env.NODE_NAME)
                    echo """
                        Your machine is ready, Use the following information to connect (RDP)
                        IP Address: ${agentNode.publicDNSName}
                        Credentials: ${ModelHyperlinkNote.encodeTo(CREDENTIALS_URL, 'View in ESS')}
                        To save costs and resources, please feel free to stop this job once you're done.
                    """
                }
            }
        }
        stage('Sleep') {
            steps {
                script {
                    echo 'Keeping VM alive for debugging'
                    sleep time: 5, unit: 'DAYS'
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>