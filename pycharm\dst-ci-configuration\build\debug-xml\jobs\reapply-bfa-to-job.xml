<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>This job is used to reapply the BFA (Build Failure Analyzer) to a specific build</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>jobUrl</name>
                    <description>The URL of the job to reapply BFA. Example: https://example.com/job/my_job/1234/</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>50</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import com.sonyericsson.jenkins.plugins.bfa.BuildFailureScanner
import jenkins.model.Jenkins

/**
* Reapply BFA to a specific job
* This script is used to reapply the BFA (Build Failure Analyzer) to a specific build
*
* reapplyBfaToJob.groovy
*/

pipeline {
    agent any
    parameters {
        string(
            name: 'jobUrl',
            defaultValue: '',
            description: 'The URL of the job to reapply BFA. Example: https://example.com/job/my_job/1234/'
        )
    }
    stages {
        stage('Reapply BFA') {
            steps {
                script {
                    // Input parameters
                    String jobUrl = params.jobUrl
                    if (!jobUrl?.trim()) {
                        error "Parameter 'jobUrl' must not be empty."
                    }
                    String jobName = ''
                    int buildNumber = 0

                    // Extract jobName and buildNumber from jobUrl (support nested jobs)
                    def matcher = jobUrl =~ /job\/(.+?)\/(\d+)(?:\/|$)/
                    if (matcher.find()) {
                        jobName = matcher.group(1).replaceAll('/job/', '/')
                        buildNumber = matcher.group(2).toInteger()
                    } else {
                        error "Invalid jobUrl format: $jobUrl. Expected format: .../job/my_job/1234/"
                    }

                    // Find the job
                    def jenkins = Jenkins.get()
                    def job = jenkins.getItemByFullName(jobName)
                    if (!job) {
                        error "Job not found: $jobName"
                    }
                    def build = job.getBuildByNumber(buildNumber)
                    if (!build) {
                        error "Build number $buildNumber not found for job $jobName"
                    }

                    // Remove old cause
                    build.removeActions(FailureCauseBuildAction)

                    // Scan and add new cause
                    def baos = new ByteArrayOutputStream()
                    def stream = new PrintStream(baos)
                    BuildFailureScanner.scanIfNotScanned(build, stream)
                    build.save()
                    stream.flush()

                    echo baos.toString()
                    echo 'Done'
                    echo "Job url: ${jobUrl}"
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>