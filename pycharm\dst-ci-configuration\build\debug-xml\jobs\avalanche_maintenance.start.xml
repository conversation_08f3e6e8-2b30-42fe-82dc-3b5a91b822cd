<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Schedule Avalanche maintenance for all nodes on this master.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>project_name=fb1-battlefieldgame</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>H 5 * * 1-5</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

import com.ea.lib.LibJenkins
import hudson.model.Node
import hudson.model.Result
import jenkins.model.Jenkins

def project = ProjectClass(env.project_name)

/**
 * avalanche_maintenance_upgrade.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger Avalanche maintenance jobs') {
            steps {
                script {
                    def jenkins_nodes = Jenkins.get().nodes
                    def jobs = [:]

                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER

                    def final_result = Result.SUCCESS

                    for (Node node in jenkins_nodes) {
                        if (!node.computer.offline) {
                            def node_name = node.nodeName

                            def args = [
                                string(name: 'Node', value: node_name),
                            ]

                            if (node.labelString.contains('cas')) {
                                def job_name = 'avalanche_maintenance_cas'
                                jobs["node_${node_name}"] = {
                                    def downstream_job = build(job: job_name, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            } else if (node.labelString.contains('wal')) {
                                def job_name = 'avalanche_maintenance_wal'
                                jobs["node_${node_name}"] = {
                                    def downstream_job = build(job: job_name, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            } else {
                                echo 'Node without maintenance job defined.'
                            }
                        }
                    }

                    // Trigger all jobs
                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    SlackMessageNew(currentBuild, '#dice-build-upgrade', project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
</flow-definition>