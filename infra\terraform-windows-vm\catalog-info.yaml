apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: terraform-windows-vm
  description: |
    Terraform project for creating and managing Windows VMs on vSphere.
  tags:
    - terraform
    - windows
    - vsphere
    - infrastructure
  annotations:
    backstage.io/techdocs-ref: dir:.
  links:
    - url: https://gitlab.ea.com/dre-cobra/silverback/terraform/terraform-windows-vm/-/pipeline_schedules
      title: VM Pipeline Schedules for Daily and Weekly Updates and Creations
      icon: build
spec:
  domain: infrastructure
  type: service
  owner: dre-cobra
  lifecycle: production
  system: onprem-build-farm
  dependsOn:
    - component:default/silverback-configs
    - component:default/vmdk-creation
