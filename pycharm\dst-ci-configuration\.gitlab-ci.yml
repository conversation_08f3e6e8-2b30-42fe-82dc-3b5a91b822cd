default:
  id_tokens:
    VAULT_JWT_TOKEN:
      aud: https://ess.ea.com
  tags:
    - glaas-shared-k8s-dind

stages:
  - build
  - test
  - deploy

# To prevent a detached pipeline that will just fail because it's running on master
workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push"'

variables:
  IMAGE_NAME: "${CI_REGISTRY_IMAGE}/build_base" # The tag will be created dynamically

  VAULT_SERVER_URL: https://ess.ea.com
  VAULT_NAMESPACE: cds-dre-prod
  VAULT_AUTH_ROLE: "gl-dre-cobra-dst-ci-configuration"
  VAULT_AUTH_PATH: "jwt/gitlab"
  VAULT_RATE_LIMIT: 50

# This step only builds the Docker image if the Dockerfile or build.gradle has changed, or if we are on the default branch.
# If the MR does not change the Dockerfile or build.gradle, it will use the image built on the default branch.
build-base-image:
  stage: build
  image: docker.artifacts.ea.com/docker:git
  services:
    - name: docker.artifacts.ea.com/docker:dind
      alias: docker
      variables:
        HEALTHCHECK_TCP_PORT: "2376"
  secrets:
    DOCKER_AUTH_CONFIG:
      vault: artifacts/automation/dre-docker-federated/ro/DOCKER_AUTH_CONFIG@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    ARTIFACTORY_USER:
      vault: artifacts/automation/dre-maven-federated/ro/username@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: true
    ARTIFACTORY_APIKEY:
      vault: artifacts/automation/dre-maven-federated/ro/reference_token@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: true
  before_script:
    - mkdir -p $HOME/.docker
    - echo "$DOCKER_AUTH_CONFIG" > $HOME/.docker/config.json
    - echo "$CI_REGISTRY_PASSWORD" | docker login "$CI_REGISTRY" -u "$CI_REGISTRY_USER" --password-stdin
    - git fetch origin "master"
    - FILES_CHANGED=$(git diff --name-only origin/master)
    - echo "$FILES_CHANGED" | grep -qE '^(build.gradle|Dockerfile)$' && DOCKER_OR_GRADLE_CHANGED=true || DOCKER_OR_GRADLE_CHANGED=false
  script: |
      docker version

      # Build image if the Dockerfile or build.gradle has changed, or if we are on the default branch
      echo -e "\033[32mDocker or Gradle changed:\033[0m \033[34m$DOCKER_OR_GRADLE_CHANGED\033[0m"
      if [ -n "$FILES_CHANGED" ]; then
        echo -e "\033[32mFiles changed:\033[0m"
          echo "$FILES_CHANGED" | while read -r file; do
            echo -e "\033[34m- $file\033[0m"
          done
      else
        echo -e "\033[32mNo files changed\033[0m"
      fi
      echo -e "\033[32mCurrent branch:\033[0m \033[34m$CI_COMMIT_BRANCH\033[0m"

      if [ "$DOCKER_OR_GRADLE_CHANGED" = true ]; then
        echo -e "\033[32mCreating image ${IMAGE_NAME}:${CI_COMMIT_REF_SLUG} from Dockerfile\033[0m"

        docker pull "${IMAGE_NAME}:${CI_COMMIT_REF_SLUG}" # Improves cache if the image already exists
        docker build --secret id=ARTIFACTORY_USER,src=$ARTIFACTORY_USER --secret id=ARTIFACTORY_APIKEY,src=$ARTIFACTORY_APIKEY --no-cache --pull -t "${IMAGE_NAME}:${CI_COMMIT_REF_SLUG}" .

        echo -e "\033[32mPushing image ${IMAGE_NAME}:${CI_COMMIT_REF_SLUG}\033[0m"
        docker push "${IMAGE_NAME}:${CI_COMMIT_REF_SLUG}"

        echo "DYNAMIC_IMAGE_TAG="${CI_COMMIT_REF_SLUG}"" >> .gitlab-ci-variables.env
      else
        echo -e "\033[32mUsing existing image ${IMAGE_NAME}:${CI_DEFAULT_BRANCH}\033[0m"
        echo "DYNAMIC_IMAGE_TAG="${CI_DEFAULT_BRANCH}"" >> .gitlab-ci-variables.env
      fi
  artifacts:
    reports:
      dotenv: .gitlab-ci-variables.env
    expire_in: 1 hour

.gradle:
  secrets:
    ARTIFACTORY_USER:
      vault: artifacts/automation/dre-maven-federated/ro/username@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    ARTIFACTORY_APIKEY:
      vault: artifacts/automation/dre-maven-federated/ro/reference_token@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
  stage: test
  image: "$IMAGE_NAME:$DYNAMIC_IMAGE_TAG"
  variables:
    GRADLEW: "./gradlew --stacktrace --info"

.test_artifacts:
  artifacts:
    expire_in: 1 week
    name: $CI_JOB_NAME
    reports:
      junit: build/test-results/**/TEST-*.xml
    when: always

.codenarc:
  extends: .gradle
  script:
    - $GRADLEW $CI_JOB_NAME -Preport=html
  artifacts:
    expose_as: "CodeNarc report"
    name: $CI_JOB_NAME
    paths:
      - build/reports/codenarc/
    expire_in: 1 week
    when: always

assemble:
  stage: test
  extends: .gradle
  script:
    - $GRADLEW assemble

codenarcMain:
  stage: test
  extends: .codenarc

codenarcTest:
  stage: test
  extends: .codenarc

editorconfig-validation:
  stage: test
  image: registry.gitlab.ea.com/dre-cobra/container-images/python-dre-cobra:latest
  variables:
    VAULT_SERVER_URL: https://ess.ea.com
    VAULT_NAMESPACE: cds-dre-prod
    VAULT_AUTH_ROLE: "gl-dre-cobra-dst-ci-configuration"
    VAULT_AUTH_PATH: "jwt/gitlab"
    ARTIFACTORY_URL: https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated/simple
  secrets:
    PYTHON_USER:
      vault: artifacts/automation/dre-pypi-federated/ro/username@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    PYTHON_PASSWORD:
      vault: artifacts/automation/dre-pypi-federated/ro/reference_token@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
  before_script:
    - echo -e "machine artifacts.ea.com\nlogin ${PYTHON_USER}\npassword ${PYTHON_PASSWORD}" > $HOME/.netrc
    - pip3 install -i $ARTIFACTORY_URL editorconfig-checker==2.4.0
  script:
    - ec
  tags:
    - glaas-shared-k8s-dind

unit:
  stage: test
  extends:
    - .gradle
    - .test_artifacts
  script:
    - $GRADLEW unit

schedulers unit tests:
  stage: test
  extends:
    - .gradle
    - .test_artifacts
  script:
    - $GRADLEW schedulers

vars:
  stage: test
  extends:
    - .gradle
    - .test_artifacts
  script:
    - $GRADLEW vars

schedulers:
  stage: test
  image: docker.artifactory.eu.ea.com/alpine:3.18 # Upgrading alpine will prevent vault to be installed
  variables:
    SCHEDULERS_PATH: src/scripts/schedulers
  secrets:
    DOCKER_AUTH_CONFIG:
      vault: artifacts/automation/dre-docker-federated/ro/DOCKER_AUTH_CONFIG@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
  script:
    - apk update
    - apk add bash curl git jq vault
    - ./resources/secrets/get_all_secrets.sh
    - ./resources/validation.sh $SCHEDULERS_PATH
  artifacts:
    expose_as: "Schedulers validation"
    paths:
      - build/reports/schedulers/validation_result.html
    expire_in: 1 week
    when: always
  rules:
    - if: '$CI_COMMIT_BRANCH == "${CI_DEFAULT_BRANCH}"'
      changes:
        - $SCHEDULERS_PATH/**/*
    - if: '$CI_COMMIT_BRANCH != "${CI_DEFAULT_BRANCH}"'

pages:
  extends: .gradle
  stage: deploy
  dependencies: [ ]
  script:
    - $GRADLEW groovydoc
    - mkdir public
    - cp -r ./build/docs/groovydoc/* ./public
  artifacts:
    paths:
      - public
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_BRANCH == "${CI_DEFAULT_BRANCH}"'

include:
  - local: .gitlab/seed_tests.yml
