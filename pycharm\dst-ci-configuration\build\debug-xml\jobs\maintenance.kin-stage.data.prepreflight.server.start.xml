<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Scheduler to run data pre-preflight on server</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>H */4 * * 1-5
H 6-23 * * 6-7</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>prepreflight_idle_length</name>
                    <description>Specifies how long(unit:minutes) node should run pre-preflight if it was idle</description>
                    <defaultValue>60</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>branch_name=kin-stage
project_name=kingston
platform=server
dataset=kindata</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile
import hudson.model.Node
import jenkins.model.Jenkins

def project = ProjectClass(env.project_name)

/**
 * data_pre_preflight_scheduler.groovy
 */

pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger pre preflight jobs') {
            steps {
                script {
                    List&lt;JobReference&gt; jobReferences = []
                    def branchFile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
                    retryOnFailureCause(3, jobReferences) {
                        // To find all the nodes with label platform + branch_name, aka the lane
                        List&lt;String&gt; jenkinsNodeNames = []
                        List&lt;Node&gt; jenkinsNodes = []
                        Jenkins.get().computers.each { computer -&gt;
                            if (
                                computer.node.labelString.contains(env.dataset) &amp;&amp;
                                    computer.node.labelString.contains(env.platform) &amp;&amp;
                                    computer.node.labelString.contains(env.branch_name)
                            ) {
                                jenkinsNodeNames.add(computer.node.nodeName)
                                jenkinsNodes.add(computer.node)
                            }
                        }

                        // Gather assets for platform
                        def nodeAssets = LibCommonCps.getDataPreflightPlatformAssets(branchFile.data_preflight_matrix, env.platform)

                        // Create downstream job name
                        def maintenanceJob = 'maintenance.' + env.branch_name + '.data.prepreflight.' + env.platform

                        // Generate platforms lists
                        def jobs = [:]

                        // Gathering neccesary values and converting/sending if needed
                        def dataPreflightReference = branchFile.preflight_settings?.datapreflight_reference_job ?: env.branch_name + '.data.lastknowngood'
                        //we do not have .data.start job in preflight master
                        def latestVerifiedData = LibJenkins.getLastStableDataChangelist(dataPreflightReference) ?: ''
                        def latestVerifiedCode = LibJenkins.getLastStableCodeChangelist(dataPreflightReference) ?: ''

                        def changelistArgs = [
                            string(name: 'code_changelist', value: latestVerifiedCode),
                            string(name: 'data_changelist', value: latestVerifiedData),
                        ]
                        def injectMap = [
                            'code_changelist': latestVerifiedCode,
                            'data_changelist': latestVerifiedData,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_ID

                        // Value for how long ago a machine has built before warming up again (can be config from stringparam), default: 45
                        def prePreflightIdleMinutes = params.prepreflight_idle_length.toInteger()
                        def prePreflightIdleMilliseconds = prePreflightIdleMinutes * 60 * 1000

                        // only loop for data nodes with correct platform label
                        for (Node node in jenkinsNodes) {
                            // Make sure node is online and not running anything (when running it is 1)
                            if (!node.computer.offline &amp;&amp; node.computer.countBusy() == 0) {
                                // Use regex pattern to determine job name validity
                                //e.g             kin-dev-unverified.kindata.preflight.linux64
                                //or  maintenance.kin-dev-unverified.data.prepreflight.linux64
                                // or maintenance.kin-dev-unverified.data.postpreflight.linux64
                                def regexPattern = "${env.branch_name}.*(preflight|postpreflight).(${env.platform})*"
                                // only return jobs ran from prePreflightIdleMilliseconds til right now
                                def nodeBuiltJobs = node.computer.builds.byTimestamp(System.currentTimeMillis() - prePreflightIdleMilliseconds, System.currentTimeMillis())
                                for (job in nodeBuiltJobs) {
                                    if (job =~ regexPattern) {
                                        // if job match pattern, remove it from jenkinsNodeNames
                                        echo 'Skipping Maintenance on: ' + node.nodeName
                                        jenkinsNodeNames.removeAll { it == node.nodeName }
                                        break
                                    }
                                }
                            } else { // for nodes which is offline or it busy when return 1
                                echo 'Skipping Maintenance on: ' + node.nodeName + ' because it is either offine or it is running jobs'
                                jenkinsNodeNames.removeAll { it == node.nodeName }
                            }
                        }

                        // for all the nodes need pre_preflight
                        for (nodeName in jenkinsNodeNames) {
                            def preflightArgs = [
                                string(name: 'node', value: "${nodeName}"),
                                string(name: 'platform', value: env.platform),
                            ]
                            // Populating asset args
                            if (nodeAssets) {
                                if (nodeAssets.size() &gt; 1) {
                                    preflightArgs &lt;&lt; string(name: 'asset', value: nodeAssets.join(' --asset '))
                                } else if (env.platform =~ '.*server') {
                                    preflightArgs &lt;&lt; string(name: 'server_asset', value: nodeAssets.first())
                                } else {
                                    preflightArgs &lt;&lt; string(name: 'asset', value: nodeAssets.first())
                                }
                            }

                            // create downstream job
                            jobs[nodeName] = {
                                def downstreamJob = build job: maintenanceJob, wait: true, propagate: false, parameters: changelistArgs + preflightArgs
                                jobReferences &lt;&lt; new JobReference(downstreamJob: downstreamJob, jobName: maintenanceJob, parameters: changelistArgs + preflightArgs, propagate: false, wait: true)
                                LibJenkins.printRunningJobs(this)
                            }
                        }

                        parallel(jobs)
                    }
                    SlackMessageNew(currentBuild, branchFile.preflight_settings?.slack_channel_preflight, project.short_name)
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>