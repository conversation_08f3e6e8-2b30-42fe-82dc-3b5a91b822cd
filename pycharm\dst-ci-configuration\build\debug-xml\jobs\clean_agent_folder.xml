<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Run a cleaning script against the selected agents</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <org.jvnet.jenkins.plugins.nodelabelparameter.NodeParameterDefinition>
                    <name>machine</name>
                    <description>select which machine to clean folders on</description>
                    <allowedSlaves/>
                    <defaultSlaves/>
                    <triggerIfResult>allowMultiSelectionForConcurrentBuilds</triggerIfResult>
                    <allowMultiNodeSelection>true</allowMultiNodeSelection>
                    <triggerConcurrentBuilds>true</triggerConcurrentBuilds>
                    <ignoreOfflineNodes>false</ignoreOfflineNodes>
                    <nodeEligibility class="org.jvnet.jenkins.plugins.nodelabelparameter.node.IgnoreOfflineNodeEligibility"/>
                </org.jvnet.jenkins.plugins.nodelabelparameter.NodeParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>delete_packages</name>
                    <defaultValue>false</defaultValue>
                    <description>Delete d:\packages</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>delete_packagesdev</name>
                    <defaultValue>false</defaultValue>
                    <description>Delete d:\packagesdev</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>delete_pip_cache</name>
                    <defaultValue>false</defaultValue>
                    <description>Delete d:\.pip-cache</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>delete_logs</name>
                    <defaultValue>false</defaultValue>
                    <description>Delete d:\dev\logs</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>delete_tnt_local</name>
                    <defaultValue>false</defaultValue>
                    <description>Delete d:\dev\tnt\local</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>delete_local_packages</name>
                    <defaultValue>false</defaultValue>
                    <description>Delete d:\dev\tnt\localpackages</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>delete_temp</name>
                    <defaultValue>false</defaultValue>
                    <description>Delete %TEMP%</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>delete_data_state</name>
                    <defaultValue>false</defaultValue>
                    <description>Delete %GAME_DATA_DIR%\.state</description>
                </hudson.model.BooleanParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>1</maxConcurrentPerNode>
            <maxConcurrentTotal>0</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
    </properties>
    <scm class="hudson.scm.NullSCM"/>
    <canRoam>true</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers/>
    <concurrentBuild>true</concurrentBuild>
    <builders>
        <hudson.tasks.BatchFile>
            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; D:\dev\logs\install-elipy.log 2&gt;&amp;1</command>
        </hudson.tasks.BatchFile>
        <hudson.tasks.BatchFile>
            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm --use-fbenv-core clean_agent --delete-packages %delete_packages% --delete-packagesdev %delete_packagesdev% --delete-pip-cache %delete_pip_cache% --delete-logs %delete_logs% --delete-tnt-local %delete_tnt_local% --delete-localpackages %delete_local_packages% --delete-temp %delete_temp% --delete-data-state %delete_data_state% --data-dir bfdata</command>
        </hudson.tasks.BatchFile>
    </builders>
    <publishers/>
    <buildWrappers>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
            <template>${JOB_NAME}.${machine}</template>
        </org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
        <hudson.plugins.build__timeout.BuildTimeoutWrapper>
            <strategy class="hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy">
                <timeoutMinutes>240</timeoutMinutes>
            </strategy>
            <operationList/>
        </hudson.plugins.build__timeout.BuildTimeoutWrapper>
        <org.jenkinsci.plugins.credentialsbinding.impl.SecretBuildWrapper>
            <bindings>
                <org.jenkinsci.plugins.credentialsbinding.impl.StringBinding>
                    <variable>VAULT_ONLINE_EXC_PROD_SECRET_ID</variable>
                    <credentialsId>cobra-online-rob-prod-secret-id</credentialsId>
                </org.jenkinsci.plugins.credentialsbinding.impl.StringBinding>
            </bindings>
        </org.jenkinsci.plugins.credentialsbinding.impl.SecretBuildWrapper>
        <com.datapipe.jenkins.vault.VaultBuildWrapper plugin="hashicorp-vault-plugin@371.v884a_4dd60fb_6">
            <configuration>
                <vaultUrl>http://127.0.0.1:8200</vaultUrl>
                <vaultCredentialId>vault-auth-dummy</vaultCredentialId>
                <failIfNotFound>true</failIfNotFound>
                <skipSslVerification>false</skipSslVerification>
                <engineVersion>2</engineVersion>
                <timeout>60</timeout>
            </configuration>
            <vaultSecrets>
                <com.datapipe.jenkins.vault.model.VaultSecret>
                    <path>artifacts/automation/dre-pypi-federated/ro</path>
                    <secretValues>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_USER</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>username</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_TOKEN</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>reference_token</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                    </secretValues>
                </com.datapipe.jenkins.vault.model.VaultSecret>
            </vaultSecrets>
            <valuesToMask/>
        </com.datapipe.jenkins.vault.VaultBuildWrapper>
    </buildWrappers>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <customWorkspace>D:\dev</customWorkspace>
</project>