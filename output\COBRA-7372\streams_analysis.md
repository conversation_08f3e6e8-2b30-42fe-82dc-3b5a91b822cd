# BCT CH1 Streams Analysis for Separate Combine Bundles Rollout

## Overview
Analysis of all BCT CH1 streams that currently have `combine_bundles` configuration to plan the rollout of separate combine bundles jobs.

## Streams with combine_bundles Configuration

### Phase 1 Target Streams (Poolbuild)

#### 1. CH1-content-dev-disc-build
- **File**: `CH1_content_dev_disc_build.groovy`
- **Machine Type**: Poolbuild (`poolbuild_data: true`, `poolbuild_frosty: true`)
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'CombinedShippingMPLevels',
      combine_reference_job: 'CH1-SP-content-dev-disc-build.patchdata.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-content-dev',
      source_branch_data: 'CH1-SP-content-dev-disc-build',
  ]
  ```
- **Frosty Matrix**: `frosty_for_patch_matrix` with win64, ps5, xbsx (combine format)
- **Status**: ✅ Ready for Phase 1

#### 2. CH1-content-dev-first-patch
- **File**: `CH1_content_dev_first_patch.groovy`
- **Machine Type**: Poolbuild (`poolbuild_patchdata: true`, `poolbuild_patchfrosty: true`)
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'CombinedShippingMPLevels',
      combine_reference_job: 'CH1-SP-content-dev-first-patch.patchdata.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-content-dev',
      source_branch_data: 'CH1-SP-content-dev-first-patch',
  ]
  ```
- **Patchfrosty Matrix**: win64, ps5, xbsx (combine format)
- **Special**: `first_patch: true`
- **Status**: ✅ Ready for Phase 1

### Phase 4/5 Target Streams (Dedicated/Mixed)

#### 3. CH1-content-dev
- **File**: `CH1_content_dev.groovy`
- **Machine Type**: Dedicated (no poolbuild settings, uses dedicated labels)
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'CombinedShippingMPLevels',
      combine_reference_job: 'CH1-SP-content-dev.deployment-data.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-content-dev',
      source_branch_data: 'CH1-SP-content-dev',
  ]
  ```
- **Frosty Matrix**: Has combine formats (win64, ps5, xbsx) with allow_failure: true
- **Status**: 🏗️ Needs dedicated machines

#### 4. CH1-release
- **File**: `CH1_release.groovy`
- **Machine Type**: Dedicated (no poolbuild settings)
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'CombinedShippingMPLevels',
      combine_reference_job: 'CH1-SP-release.patchdata.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-release',
      source_branch_data: 'CH1-SP-release',
  ]
  ```
- **Status**: 🏗️ Needs dedicated machines

#### 5. CH1-stage
- **File**: `CH1_stage.groovy`
- **Machine Type**: Dedicated (no poolbuild settings)
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'CombinedShippingMPLevels',
      combine_reference_job: 'CH1-SP-stage.patchdata.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-stage',
      source_branch_data: 'CH1-SP-stage',
  ]
  ```
- **Status**: 🏗️ Needs dedicated machines

#### 6. CH1-qol
- **File**: `CH1_qol.groovy`
- **Machine Type**: Dedicated (no poolbuild settings)
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'CombinedShippingMPLevels',
      combine_reference_job: 'CH1-SP-release.patchdata.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-release',
      source_branch_data: 'CH1-SP-release',
  ]
  ```
- **Status**: 🏗️ Needs dedicated machines

### BF Labs Streams

#### 7. CH1-bflabs-release
- **File**: `CH1_bflabs_release.groovy`
- **Machine Type**: Dedicated (no poolbuild settings)
- **Special Features**: EAC enabled, custom combine settings files
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'Game/Setup/Build/LabsClientLevels',
      combine_reference_job: 'CH1-SP-release.patchdata.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-release',
      source_branch_data: 'CH1-SP-release',
  ]
  ```
- **Status**: 🏗️ Needs dedicated machines

#### 8. CH1-bflabs-stage
- **File**: `CH1_bflabs_stage.groovy`
- **Machine Type**: Dedicated (no poolbuild settings)
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'Game/Setup/Build/LabsClientLevels',
      combine_reference_job: 'CH1-SP-stage.patchdata.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-stage',
      source_branch_data: 'CH1-SP-stage',
  ]
  ```
- **Status**: 🏗️ Needs dedicated machines

#### 9. CH1-bflabs-qol
- **File**: `CH1_bflabs_qol.groovy`
- **Machine Type**: Dedicated (no poolbuild settings)
- **Current Config**:
  ```groovy
  combine_bundles: [
      combine_asset: 'Game/Setup/Build/LabsClientLevels',
      combine_reference_job: 'CH1-SP-release.patchdata.start',
      is_target_branch: true,
      source_branch_code: 'CH1-SP-release',
      source_branch_data: 'CH1-SP-release',
  ]
  ```
- **Status**: 🏗️ Needs dedicated machines

### Non-Target Streams (No combine_bundles or disabled)

#### CH1-SP-* Streams
- **CH1-SP-content-dev-disc-build**: `is_target_branch: false`
- **CH1-SP-content-dev-first-patch**: `is_target_branch: false`
- Other CH1-SP-* streams: No combine_bundles configuration

## Rollout Strategy Summary

### Phase 1: Initial Poolbuild Rollout
- **Target**: CH1-content-dev-disc-build, CH1-content-dev-first-patch
- **Platform**: Start with win64 only
- **Machine Type**: Poolbuild
- **Risk**: Low (poolbuild machines available)

### Phase 2: Expand Platforms
- **Target**: Same streams as Phase 1
- **Platforms**: Add ps5, xbsx
- **Machine Type**: Poolbuild
- **Risk**: Low (same machine pool)

### Phase 3: Additional Poolbuild Streams
- **Target**: Any other poolbuild streams identified during rollout
- **Platforms**: All (win64, ps5, xbsx)
- **Machine Type**: Poolbuild
- **Risk**: Medium (need to identify additional poolbuild streams)

### Phase 4: Plan Dedicated Machines
- **Target**: All dedicated streams
- **Requirements**: New dedicated machine labels
- **Risk**: High (requires infrastructure changes)

### Phase 5: Dedicated Machine Rollout
- **Target**: CH1-content-dev, CH1-release, CH1-stage, CH1-qol, CH1-bflabs-*
- **Platforms**: All
- **Machine Type**: Dedicated
- **Risk**: High (new infrastructure)

## Next Steps
1. ✅ Confirmed machine types for all streams
2. Start Phase 1 implementation
3. Plan dedicated machine requirements
4. Coordinate with infrastructure team for new machines
