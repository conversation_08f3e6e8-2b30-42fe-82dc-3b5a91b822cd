<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Triggers a dvcs push &amp; fetch for remote spec nfs-frostbite.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>H/5 * * * 1-6</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>project_name=nfsupgrade
script_branch_name=upgrade
dvcs_command=null
dvcs_remote_spec=nfs-frostbite
slack_channel=nfs-dvcs
workspace_root=D:\dev
skip_fetch=false
skip_push=false</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import hudson.model.Result

def project = ProjectClass(env.project_name)

/**
 * perforce_dvcs_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger dvcs job') {
            steps {
                script {
                    def dvcs_remote_spec = env.dvcs_remote_spec
                    def slack_channel = env.slack_channel
                    def skip_fetch = env.skip_fetch.toBoolean()
                    def skip_push = env.skip_push.toBoolean()
                    def args = []
                    def inject_map = [
                        'dvcs_remote_spec': dvcs_remote_spec,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME

                    def final_result = Result.SUCCESS

                    if (!skip_push) {
                        def job_push_name = 'dvcs.push.' + dvcs_remote_spec
                        def push_job = build(job: job_push_name, parameters: args, propagate: false)
                        final_result = push_job.result
                    }
                    if (final_result.toString() != 'SUCCESS' &amp;&amp; !skip_push) {
                        echo 'DVCS push failed, aborting.'
                    } else if (!skip_fetch) {
                        def job_fetch_name = 'dvcs.fetch.' + dvcs_remote_spec
                        def fetch_job = build(job: job_fetch_name, parameters: args, propagate: false)
                        final_result = fetch_job.result
                    }

                    currentBuild.result = final_result.toString()
                    SlackMessageNew(currentBuild, slack_channel, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, true) } }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>