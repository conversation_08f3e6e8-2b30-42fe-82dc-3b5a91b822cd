# CH1-content-dev-disc-build Combined Bundles Job Analysis Report

**Date:** July 17, 2025  
**Task:** Investigate missing combined_bundles job for CH1-content-dev-disc-build branch  
**Jenkins Controller:** bct-ch1-dev  
**Commit:** 52672c20600ee89d9feaff3d696537ac8fab377a (feat: [COBRA-7372] rollout separated combine_bundle job for CH1_content_dev disc and 1st patch)

## Executive Summary

**Issue Status:** PARTIALLY RESOLVED - Jobs exist but analysis needs correction  
**Root Cause:** Naming convention mismatch AND potential missing dedicated combine_bundle job

The "combined_bundles" job naming doesn't exist, but there's a more important finding: The commit message indicates this should be a "separated combine_bundle job" but the disc-build branch is still using the regular `frosty.bfdata` path instead of a dedicated `patchfrosty.bfdata` path like the first-patch branch.

## Detailed Analysis

### 1. Branch Processing Analysis

The CH1-content-dev-disc-build branch is being processed correctly by the <PERSON> seed job:

```
Processing branch: CH1-content-dev-disc-build
    Processing frostbite_syncer_setup...
    Processing frostbite_syncer_setup...
    Processing patchdata_matrix...
    Processing patchfrosty_matrix...
    Processing patchdata_matrix [name:win64]...
    Processing patchdata_matrix [name:ps5]...
    Processing patchdata_matrix [name:xbsx]...
    Processing frosty_job_matrix win64 [format:combine, config:final, region:ww, args:]...
    Processing frosty_job_matrix ps5 [format:combine, config:final, region:ww, args:]...
    Processing frosty_job_matrix xbsx [format:combine, config:final, region:ww, args:]...
```

### 2. Generated Jobs

The following jobs were successfully generated for CH1-content-dev-disc-build:

#### Combine Jobs (Current implementation - using regular frosty path):
- `CH1-content-dev-disc-build.frosty.bfdata.ps5.combine.ww.final`
- `CH1-content-dev-disc-build.frosty.bfdata.win64.combine.ww.final`
- `CH1-content-dev-disc-build.frosty.bfdata.xbsx.combine.ww.final`

#### Expected Dedicated Jobs (based on first-patch pattern):
**Should be:**
- `CH1-content-dev-disc-build.patchfrosty.bfdata.ps5.combine.ww.final`
- `CH1-content-dev-disc-build.patchfrosty.bfdata.win64.combine.ww.final`
- `CH1-content-dev-disc-build.patchfrosty.bfdata.xbsx.combine.ww.final`

#### Comparison with first-patch branch (which has dedicated jobs):
- `CH1-content-dev-first-patch.patchfrosty.bfdata.ps5.combine.ww.final`
- `CH1-content-dev-first-patch.patchfrosty.bfdata.win64.combine.ww.final`
- `CH1-content-dev-first-patch.patchfrosty.bfdata.xbsx.combine.ww.final`

#### Other Jobs:
- `CH1-content-dev-disc-build.patchdata-combine.bfdata.ps5`
- `CH1-content-dev-disc-build.patchdata-combine.bfdata.win64`
- `CH1-content-dev-disc-build.patchdata-combine.bfdata.xbsx`
- `CH1-content-dev-disc-build.patchdata.start`
- `CH1-content-dev-disc-build.patchfrosty.start`
- `CH1-content-dev-disc-build.store_regular_baseline.start`
- `CH1-content-dev-disc-build.store_regular_baseline_builds`

### 3. Naming Convention Analysis

#### Current Implementation:
- **Format:** `{branch}.frosty.bfdata.{platform}.combine.{region}.{config}`
- **Examples:** 
  - `CH1-content-dev-disc-build.frosty.bfdata.win64.combine.ww.final`
  - `CH1-content-dev.frosty.bfdata.win64.combine.ww.final`

#### User Expectation:
- **Expected:** Jobs with "combined_bundles" in the name
- **Reality:** No such naming convention exists in the current implementation

### 4. Comparison with Other Branches

#### Regular CH1-content-dev Branch:
- Generates both `files` and `combine` format jobs
- Uses `frosty_job_matrix` processing
- Example: `CH1-content-dev.frosty.bfdata.win64.combine.ww.final`

#### CH1-content-dev-disc-build Branch:
- Generates only `combine` format jobs
- Uses `patchfrosty_matrix` processing
- Example: `CH1-content-dev-disc-build.frosty.bfdata.win64.combine.ww.final`

#### CH1-content-dev-first-patch Branch:
- Similar pattern to disc-build
- Uses `patchfrosty_matrix` processing
- Example: `CH1-content-dev-first-patch.patchfrosty.bfdata.win64.combine.ww.final`

### 5. Commit Context Analysis

The commit message states: "feat: [COBRA-7372] rollout separated combine_bundle job for CH1_content_dev disc and 1st patch"

- **Commit mentions:** "combine_bundle" (singular, underscore)
- **User expects:** "combined_bundles" (plural, underscore)
- **Actual implementation:** "combine" (singular, no underscore)

This indicates the terminology evolved during implementation.

## Root Cause

**The "combined_bundles" job is not missing - it never existed with that name, BUT there's a more significant issue:**

Based on the commit message "rollout separated combine_bundle job for CH1_content_dev disc and 1st patch", the disc-build branch should have dedicated combine_bundle jobs similar to the first-patch branch pattern.

**Current State:**
- **disc-build**: Uses regular `frosty.bfdata` path (same as regular CH1-content-dev)
- **first-patch**: Uses dedicated `patchfrosty.bfdata` path (properly separated)

**Expected State (based on commit intention):**
- **disc-build**: Should use dedicated `patchfrosty.bfdata` path for true separation

**The actual issue:** The disc-build branch is NOT using separated/dedicated jobs - it's reusing the regular frosty jobs, while the first-patch branch correctly uses dedicated patchfrosty jobs.

**New job names should be:**
- `CH1-content-dev-disc-build.patchfrosty.bfdata.ps5.combine.ww.final`
- `CH1-content-dev-disc-build.patchfrosty.bfdata.win64.combine.ww.final`
- `CH1-content-dev-disc-build.patchfrosty.bfdata.xbsx.combine.ww.final`

## Recommendations

### 1. Immediate Actions

1. **Verify the DSL script configuration** for CH1-content-dev-disc-build to ensure it's using `patchfrosty_matrix` instead of `frosty_job_matrix`

2. **Expected dedicated job names** after proper implementation:
   - `CH1-content-dev-disc-build.patchfrosty.bfdata.ps5.combine.ww.final`
   - `CH1-content-dev-disc-build.patchfrosty.bfdata.win64.combine.ww.final`
   - `CH1-content-dev-disc-build.patchfrosty.bfdata.xbsx.combine.ww.final`

3. **Current workaround**: The existing jobs work but aren't properly separated:
   - `CH1-content-dev-disc-build.frosty.bfdata.ps5.combine.ww.final`
   - `CH1-content-dev-disc-build.frosty.bfdata.win64.combine.ww.final`
   - `CH1-content-dev-disc-build.frosty.bfdata.xbsx.combine.ww.final`

### 2. Technical Investigation Required

1. **Check the DSL script** to see why disc-build is using `frosty_job_matrix` instead of `patchfrosty_matrix`
2. **Verify the intended behavior** - should disc-build have dedicated jobs like first-patch?
3. **Update the DSL script** if disc-build should use dedicated patchfrosty jobs

### 2. Process Improvements

1. **Standardize terminology** across commit messages, documentation, and implementation
2. **Create a job naming convention guide** for the team
3. **Implement job search/discovery tools** to help users find jobs with similar functionality

### 3. Communication

1. **Clarify with the user** what specific functionality they need
2. **Verify** that the existing "combine" jobs meet their requirements
3. **Provide training** on the current job naming conventions

## Technical Details

### DSL Script Processing Pattern:
```groovy
// For disc-build branches
patchfrosty_matrix {
    // Generates combine format jobs
    frosty_job_matrix platform [format:combine, config:final, region:ww, args:]
}
```

### Job Naming Pattern:
```
{branch_name}.frosty.bfdata.{platform}.{format}.{region}.{config}
```

Where:
- `branch_name`: CH1-content-dev-disc-build
- `platform`: win64, ps5, xbsx
- `format`: combine
- `region`: ww (worldwide)
- `config`: final

## Conclusion

**ISSUE IDENTIFIED:** The CH1-content-dev-disc-build branch is NOT properly implementing separated combine_bundle jobs as intended by the commit.

**Current State:**
- disc-build jobs use regular `frosty.bfdata` path (NOT separated)
- first-patch jobs use dedicated `patchfrosty.bfdata` path (properly separated)

**The dedicated combine_bundle job names should be:**
- `CH1-content-dev-disc-build.patchfrosty.bfdata.ps5.combine.ww.final`
- `CH1-content-dev-disc-build.patchfrosty.bfdata.win64.combine.ww.final`  
- `CH1-content-dev-disc-build.patchfrosty.bfdata.xbsx.combine.ww.final`

**Status:** DSL script needs to be updated to use `patchfrosty_matrix` for disc-build branch to create truly separated combine_bundle jobs as intended.
