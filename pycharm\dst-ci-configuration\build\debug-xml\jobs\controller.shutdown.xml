<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Shutdown the <PERSON> controller when no jobs are running --&gt; the supervisor will restart a fresh container.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>0 0 * * 7</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>wait_doquite_hours</name>
                    <description>Specifies how long (hours) to wait for master to quit before restart.</description>
                    <defaultValue>18</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>wait_forcekill_hours</name>
                    <description>Specifies how long (hours) to wait extra before force restart</description>
                    <defaultValue>6</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>restart_nodes</name>
                    <description>Set true to restart Jenkins nodes</description>
                    <defaultValue>true</defaultValue>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>restart_controller</name>
                    <description>Set true to restart Jenkins controller</description>
                    <defaultValue>true</defaultValue>
                </hudson.model.BooleanParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>30</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

import com.ea.lib.LibSlack
import hudson.Launcher
import hudson.model.Computer
import hudson.model.FreeStyleBuild
import hudson.model.Job
import hudson.util.StreamTaskListener
import jenkins.model.Jenkins

import java.time.Duration
import java.time.Instant

/**
 * restartCloudControllerAndAgents.groovy
 * Tries to initiate Jenkins restart if no jobs are running within $wait_doquite_hours hours
 * Eventually kills all running jobs after $wait_forcekill_hours hours and puts them in the queue instead.
 * After that schedules a restart.
 */

pipeline {
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    agent { label 'master' }
    stages {
        stage('Run') {
            steps {
                script {
                    echo '--&gt; Job Start'
                    if (params.restart_controller || params.restart_nodes) {
                        disableJobs(this)
                        if (numberOfRunningBuilds() &gt; 1) {
                            echo "--&gt; There are still ${numberOfRunningBuilds()} busy executor(s) on the controller, continue waiting..."
                            rescheduleAndKillJobs(this)
                        } else {
                            echo '--&gt; This is the only running job, can continue restart'
                        }

                        if (params.restart_nodes) {
                            rebootJenkinsNodes(this)
                        }
                        if (params.restart_controller) {
                            restartJenkinsController(this)
                        } else {
                            // disableJobs does not persist a controller restart. If no restart occurs, re-enable jobs instead
                            enableJobs(this)
                        }
                    } else {
                        echo '--&gt; No controller or node restart configured. Skipping job...'
                    }
                    echo '--&gt; Job complete'
                }
            }
        }
    }
    post {
        changed {
            script {
                LibSlack.forPipelines(this, '#cobra-outage-jenkins', 'cob')
            }
        }
    }
}

/**
 * disable jobs on the controller. Note that disabling jobs from here does not survive a controller reboot
 */
void disableJobs(context) {
    Jenkins controller = Jenkins.get()
    List&lt;Job&gt; jobs = controller.getItems(Job)
    def controllerNode = controller.toComputer()
    tryToFindWindow(context, params.wait_doquite_hours.toInteger(), controllerNode) {
        context.echo '--&gt; Disable all Jobs - preparation for a restart.'
        for (Job job in jobs.findAll { it.triggers &amp;&amp; ![currentBuild.projectName, 'seed'].contains(it.name) }) {
            context.echo "--&gt; Disabling ${job.name}"
            job.disabled = true
        }
    }
}

/**
 * enable jobs on the controller
 */
void enableJobs(context) {
    Jenkins controller = Jenkins.get()
    List&lt;Job&gt; jobs = controller.getItems(Job)
    def controllerNode = controller.toComputer()
    tryToFindWindow(context, params.wait_doquite_hours.toInteger(), controllerNode) {
        context.echo '--&gt; Re-enable Jobs'
        for (Job job in jobs.findAll { it.triggers &amp;&amp; ![currentBuild.projectName, 'seed'].contains(it.name) }) {
            context.echo "--&gt; Enabling ${job.name}"
            job.disabled = false
        }
    }
}

/**
* Reschedule running jobs to recover as soon as possible following the shutdown process. Then kill all jobs
*/
void rescheduleAndKillJobs(context) {
    Jenkins controller = Jenkins.get()
    List&lt;Job&gt; jobs = controller.getItems(Job)
    def controllerNode = controller.toComputer()
    tryToFindWindow(context, params.wait_forcekill_hours.toInteger(), controllerNode) {
        def runningBuilds = (jobs.findAll { it.name != currentBuild.projectName &amp;&amp; it.building }*.builds).flatten().findAll { it.building }
        def numberOfRunningBuilds = runningBuilds.size()
        for (run in runningBuilds) {
            run.class == FreeStyleBuild ? run.doStop() : run.doKill()
            def job = run.parent
            if (!job.inQueue) {
                int quietPeriod = Math.max(numberOfRunningBuilds * 60 * 2, 600)
                job.scheduleBuild2(quietPeriod)
            }
        }

        def maxWaitMinutes = numberOfRunningBuilds // wait 1 minute per job
        waitForKilledJobsToStop(context, maxWaitMinutes)
        if (controllerNode.countBusy() &gt; 1) {
            context.echo '--&gt; Timed out waiting for killed jobs to stop'
        }
    }
}

/**
 * Try to find a window where not Jobs are running on the controller (except this one)
 * @param forHours How many hours should we try for
 * @param controllerNode The jenkins controller node
 * @param andThen What should run after the wait time has expired or a window is found
 */
void tryToFindWindow(context, Integer forHours, Computer controllerNode, Closure andThen) {
    def startTime = Instant.now()
    long numWaitedHours = 0
    while (controllerNode.countBusy() &gt; 1 &amp;&amp; numWaitedHours &lt; forHours) {
        // we assume that all running jobs have been scheduled by controller
        context.echo "${controllerNode.displayName} is busy"
        // print running jobs
        for (executor in controllerNode.executors) {
            if (executor.currentExecutable) {
                context.echo " &gt; ${executor.currentExecutable}"
            }
        }
        int sleepSeconds = 60
        context.echo "--&gt; Jobs are still running. Sleeping for $sleepSeconds seconds.\n"
        Thread.sleep(sleepSeconds * 1000)
        numWaitedHours = Duration.between(startTime, Instant.now()).toHours()
        context.echo "--&gt; DEBUG: startTime: ${startTime}, currenTime: ${Instant.now()}, numHoursWaited: ${numWaitedHours}. numHoursToWait: ${forHours}"
    }
    andThen()
}

/**
 * Reboot all nodes connected to this controller
 */
void rebootJenkinsNodes(context) {
    context.echo '--&gt; Rebooting all nodes.'
    runCommandOnAllNodes(
        context,
        'echo Nothing to delete',
        'cmd /c del /Q C:\\jenkins_agent.pid C:\\JenkinsSlave\\jenkins_agent.pid',
        'Deleted PID file for '
    )
    runCommandOnAllNodes(context, 'reboot', 'shutdown /r /t 1', 'Rebooted ')
}

/**
 * For all the jobs that we have tried to kill to stop
 * @param controller The jenkins controller instance
 * @param maxWaitMinutes How long should we wait for jobs to stop running
 */
void waitForKilledJobsToStop(context, int maxWaitMinutes) {
    // Sometimes Jenkins seems to need a few moments to finish the doStop() and doKill() calls.
    // I haven't been able to figure out when or why that happens.
    def startTime = Instant.now()
    int sleepSeconds = 10
    while (Duration.between(startTime, Instant.now()).toMinutes() &lt; maxWaitMinutes &amp;&amp; numberOfRunningBuilds() &gt; 1) {
        context.echo "--&gt; Waiting for killed jobs to actually stop (${numberOfRunningBuilds()} jobs left). Sleeping for $sleepSeconds seconds.\n"
        Thread.sleep(sleepSeconds * 1000)
        sleepSeconds += 10
    }
}

/**
 * Run a give command on all nodes. Unix command will run on Unix based nodes and the windows command will run on Windows nodes
 * @param unixCommand The command to run on Unix nodes
 * @param windowsCommand The command to run on Windows nodes
 * @param successMessagePrefix What message to show after the command was ran successfully
 */
void runCommandOnAllNodes(context, String unixCommand, String windowsCommand, String successMessagePrefix) {
    for (Node node in Jenkins.get().nodes) {
        runCommandOnNode(context, node, unixCommand, windowsCommand, successMessagePrefix)
    }
}

/**
 * Run a give command on a given node. Unix command will run on Unix based nodes and the windows command will run on Windows nodes
 * @param node What node should we try and run the command on
 * @param unixCommand The command to run on Unix nodes
 * @param windowsCommand The command to run on Windows nodes
 * @param successMessagePrefix What message to show after the command was ran successfully
 */
void runCommandOnNode(context, Node node, String unixCommand, String windowsCommand, String successMessagePrefix) {
    if (node.toComputer().online) {
        StreamTaskListener listener = new StreamTaskListener(new ByteArrayOutputStream())
        Launcher launcher = node.createLauncher(listener)
        Launcher.ProcStarter process = Launcher.ProcStarter.newInstance()
        process.stdout(listener).pwd(node.rootPath)
        process.cmdAsSingleString(launcher.unix ? unixCommand : windowsCommand)
        try {
            launcher.launch(process).join()
            context.echo "$successMessagePrefix ${node.nodeName}"
        }
        catch (Exception ex) {
            context.echo ex.message
            context.echo "\tat ${ex.stackTrace.join('\n\tat ')}"
        }
    } else {
        context.echo "Node ${node.displayName} is offline. Skipping."
    }
}

/**
 * Get number of running builds on Jenkins
 */
Boolean numberOfRunningBuilds() {
    Jenkins controller = Jenkins.get()
    return controller.toComputer().countBusy()
}

/**
 * Restart controller
 */
void restartJenkinsController(context) {
    context.echo '--&gt; Restart Jenkins service!'
    Jenkins controller = Jenkins.get()
    controller.restart()
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>