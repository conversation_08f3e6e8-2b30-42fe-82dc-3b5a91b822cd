<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Test job to trigger a job remotely on dice-cas-dev.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies which code changelist that will be sent to dice-cas-dev.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Specifies which data changelist that will be sent to dice-cas-dev.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <scm class="hudson.scm.NullSCM"/>
    <canRoam>false</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers/>
    <concurrentBuild>false</concurrentBuild>
    <builders>
        <org.jenkinsci.plugins.ParameterizedRemoteTrigger.RemoteBuildConfiguration>
            <token/>
            <remoteJenkinsName>kin-dev-jenkins.cobra.dre.ea.com</remoteJenkinsName>
            <job>trigger-from.upgrade-validator</job>
            <shouldNotFailBuild>true</shouldNotFailBuild>
            <pollInterval>10</pollInterval>
            <preventRemoteBuildQueue>false</preventRemoteBuildQueue>
            <blockBuildUntilComplete>true</blockBuildUntilComplete>
            <parameters>code_changelist=${code_changelist}
data_changelist=${data_changelist}</parameters>
            <parameterList>
                <string>code_changelist=${code_changelist}</string>
                <string>data_changelist=${data_changelist}</string>
            </parameterList>
            <overrideAuth>true</overrideAuth>
            <auth>
                <org.jenkinsci.plugins.ParameterizedRemoteTrigger.Auth>
                    <authType>credentialsPlugin</authType>
                    <creds>perforce-p4buildedge02-fb-kingston01</creds>
                    <NONE>none</NONE>
                    <API__TOKEN>apiToken</API__TOKEN>
                    <CREDENTIALS__PLUGIN>credentialsPlugin</CREDENTIALS__PLUGIN>
                </org.jenkinsci.plugins.ParameterizedRemoteTrigger.Auth>
            </auth>
            <loadParamsFromFile>false</loadParamsFromFile>
            <parameterFile>null</parameterFile>
            <abortTriggeredJob>false</abortTriggeredJob>
            <maxConn>1</maxConn>
            <enhancedLogging>false</enhancedLogging>
            <useCrumbCache>true</useCrumbCache>
            <useJobInfoCache>true</useJobInfoCache>
            <disabled>false</disabled>
            <overrideTrustAllCertificates>false</overrideTrustAllCertificates>
            <trustAllCertificates>false</trustAllCertificates>
            <queryString/>
        </org.jenkinsci.plugins.ParameterizedRemoteTrigger.RemoteBuildConfiguration>
    </builders>
    <publishers/>
    <buildWrappers>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
            <template>${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}</template>
        </org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
        <hudson.plugins.build__timeout.BuildTimeoutWrapper>
            <strategy class="hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy">
                <timeoutMinutes>60</timeoutMinutes>
            </strategy>
            <operationList>
                <hudson.plugins.build__timeout.operations.FailOperation/>
                <hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
                    <description>Build failed due to timeout after {0} minutes</description>
                </hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
            </operationList>
        </hudson.plugins.build__timeout.BuildTimeoutWrapper>
    </buildWrappers>
    <assignedNode>master</assignedNode>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</project>