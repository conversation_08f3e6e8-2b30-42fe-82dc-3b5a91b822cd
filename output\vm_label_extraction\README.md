# VM Label Extraction Pattern for AutotestMatrix.groovy

## Pattern Summary
To determine all VM labels (platforms) that should be created for a given `*AutotestMatrix.groovy` file:
- Each matrix file defines multiple `TestInfo` objects, each with a `platforms` field (a list of `Platform` objects).
- Each `Platform` object has a `name` (e.g., `Name.PS5`, `Name.WIN64`, `Name.XBSX`) and sometimes a `region`.
- The set of all unique `Platform.name` values across all test categories and branches represents the VM labels you should create.

## Extraction Script
A Python script to extract all unique VM labels from a Groovy matrix file:

```python
import re
import sys
from typing import Set

def extract_vm_labels(file_path: str) -> Set[str]:
    """
    Extract all unique VM labels (platform names) from a Groovy AutotestMatrix file.
    Args:
        file_path (str): Path to the Groovy file.
    Returns:
        Set[str]: Set of unique platform names (labels).
    """
    label_pattern = re.compile(r'Platform\(name: Name\.([A-Z0-9_]+)')
    labels = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            for match in label_pattern.findall(line):
                labels.add(match)
    return labels

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python extract_vm_labels.py <AutotestMatrix.groovy>")
        sys.exit(1)
    file_path = sys.argv[1]
    labels = extract_vm_labels(file_path)
    print("Unique VM labels found:")
    for label in sorted(labels):
        print(label)
```

## Example Output
For `BctCh1AutotestMatrix.groovy`, the script found:

```
Unique VM labels found:
LINUX64
PS5
TOOL
WIN64
WIN64DLL
XBSX
```

---
This pattern and script can be used for any `*AutotestMatrix.groovy` file to reliably determine all VM labels needed for test execution.
