# COBRA-7372 Battlefield CI Pipeline: Combined Bundles Job Investigation Summary

## Task
- Investigate and verify output path and job creation logic for "combine bundles" in Battlefield CI pipeline after refactoring for rollout ticket COBRA-7372.
- Ensure job `CH1-content-dev-disc-build.patchdata-combine.bfdata.ps5` produces correct output and determine new job name/output path logic for dedicated combine bundle jobs.
- Diagnose why expected Jenkins job named `combined_bundles` does not appear on Jenkins controller.

## Findings
- **Matrix and job settings** for combined_bundles are correct in `CH1_content_dev_disc_build.groovy`:
  - `use_separate_combined_job: true`
  - `combined_job_platforms: ['win64', 'ps5', 'xbsx']`
- **Output path logic** for combined_bundles is correct and matches expectations.
- **No <PERSON> seed job, Job DSL script, or Jenkinsfile** found in the workspace. Job generation logic is not present in batch files or pipeline scripts here.
- **Jenkins job creation** is likely managed externally (Jenkins UI or another repository).

## Recommendations
- **Review Jenkins seed job and Job DSL script** in the Jenkins UI or relevant repository to ensure combined_bundles jobs are generated.
- **Check <PERSON> seed job console output** for errors or warnings related to job creation.
- **Update or rerun the seed job** if necessary to generate the missing combined_bundles jobs.
- **Verify job naming patterns** in the Job DSL script to match expected combined_bundles job names.

## COBRA-7372 Combined Bundles Job Investigation Summary

### Root Cause
The Jenkins job for combined bundles was missing because the seed job logic in `basic_jobs.groovy` only created jobs named `<branch>.patchdata-combine.<dataset>.<platform>`, not `<branch>.combined_bundles.<platform>`. The expected job name did not match the actual job name generated by the Job DSL.

### Resolution
The seed job logic in `basic_jobs.groovy` was updated to also create a job named `<branch>.combined_bundles.<platform>` alongside the existing `patchdata-combine` job when `combine_bundles` is set. This ensures the Jenkins job appears with the expected name and is discoverable by downstream systems and users.

#### Change Details
- Added creation of `<branch>.combined_bundles.<platform>` job in the same block as `patchdata-combine` job.
- The new job uses the same configuration and parameters as the existing combine bundles job.

### Next Steps
- Rerun the Jenkins seed job to regenerate jobs and verify that `<branch>.combined_bundles.<platform>` appears as expected.
- Confirm output path and job execution for combine bundles.
- Update documentation and notify stakeholders of the change.

---
**Time Tracking:**
- Start: [Please fill in actual start time]
- End: [Please fill in actual end time]
- Duration: [Please fill in actual duration]

---
**Agent:** GitHub Copilot
**Date:** 2024-07-16
