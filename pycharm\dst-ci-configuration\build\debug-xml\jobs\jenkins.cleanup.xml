<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Cleanup $JENKINS_HOME/monitoring and $JENKINS_HOME/fingerprints; approve all scripts; remove secrets from environment</description>
    <keepDependencies>false</keepDependencies>
    <properties/>
    <scm class="hudson.scm.NullSCM"/>
    <canRoam>false</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers>
        <hudson.triggers.TimerTrigger>
            <spec>H/5 * * * 1-6
H/5 6-23 * * 7</spec>
        </hudson.triggers.TimerTrigger>
        <jenkins.triggers.ReverseBuildTrigger>
            <spec/>
            <upstreamProjects>seed</upstreamProjects>
            <threshold>
                <name>UNSTABLE</name>
                <ordinal>1</ordinal>
                <color>YELLOW</color>
                <completeBuild>true</completeBuild>
            </threshold>
        </jenkins.triggers.ReverseBuildTrigger>
    </triggers>
    <concurrentBuild>false</concurrentBuild>
    <builders>
        <hudson.tasks.Shell>
            <command>
set +x

FINGERPRINTS=$JENKINS_HOME/fingerprints
if [ ! -d "$FINGERPRINTS" ]; then
    echo Error: $FINGERPRINTS does not exist.
    exit 1
fi
echo Cleaning up $FINGERPRINTS contents
set +e; rm -rf $FINGERPRINTS/*; set -e;
echo Done

MONITORING=$JENKINS_HOME/monitoring
if [ ! -d "$MONITORING" ]; then
    echo Error: $MONITORING does not exist.
    exit 1
fi
echo Cleaning up old files from $MONITORING
set +e; find $MONITORING -mindepth 1 -mtime +30 -delete; set -e;
echo Done
</command>
        </hudson.tasks.Shell>
    </builders>
    <publishers/>
    <buildWrappers>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <hudson.plugins.build__timeout.BuildTimeoutWrapper>
            <strategy class="hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy">
                <timeoutMinutes>240</timeoutMinutes>
            </strategy>
            <operationList>
                <hudson.plugins.build__timeout.operations.FailOperation/>
                <hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
                    <description>Build failed due to timeout after {0} minutes</description>
                </hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
            </operationList>
        </hudson.plugins.build__timeout.BuildTimeoutWrapper>
    </buildWrappers>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>50</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <assignedNode>master</assignedNode>
</project>