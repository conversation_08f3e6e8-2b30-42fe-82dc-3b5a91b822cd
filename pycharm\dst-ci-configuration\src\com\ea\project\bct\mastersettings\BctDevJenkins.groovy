package com.ea.project.bct.mastersettings

import com.ea.project.bct.Bct
import com.ea.project.fb1.Fb1Battlefieldgame

class Bct<PERSON><PERSON><PERSON><PERSON><PERSON> {
    static Class project = Bct
    static Map branches = [
        'trunk-content-dev'        : [
            code_folder         : 'mainline',
            code_branch         : 'trunk-content-dev',
            data_folder         : 'mainline',
            data_branch         : 'trunk-content-dev',
            koala_content       : true,
            job_label_poolbuild : 'poolbuild_trunk-content-dev',
            job_label_statebuild: 'statebuild_trunk-content-dev'
        ],
        'ecs-splines'              : [
            code_folder: 'tasks',
            code_branch: 'ecs-splines',
            data_folder: 'tasks',
            data_branch: 'ecs-splines'
        ],
        'trunk-code-dev'           : [
            code_folder         : 'mainline',
            code_branch         : 'trunk-code-dev',
            data_folder         : 'mainline',
            data_branch         : 'trunk-code-dev',
            koala_code          : true,
            job_label_poolbuild : 'poolbuild_trunk-code-dev',
            job_label_statebuild: 'statebuild_trunk-code-dev',
        ],
        'trunk-code-dev-sanitizers': [
            code_folder            : 'mainline',
            code_branch            : 'trunk-code-dev-sanitizers',
            data_folder            : 'mainline',
            data_branch            : 'trunk-code-dev-sanitizers',
            non_virtual_code_branch: 'trunk-code-dev',
            non_virtual_data_branch: 'trunk-code-dev'
        ],
        'trunk-content-dev-test'   : [
            code_folder            : 'mainline',
            code_branch            : 'trunk-content-dev-test',
            data_folder            : 'mainline',
            data_branch            : 'trunk-content-dev-test',
            non_virtual_code_branch: 'trunk-content-dev',
            non_virtual_data_branch: 'trunk-content-dev'
        ],
        'trunk-content-dev-cache'  : [
            code_folder            : 'mainline',
            code_branch            : 'trunk-content-dev-cache',
            data_folder            : 'mainline',
            data_branch            : 'trunk-content-dev-cache',
            non_virtual_code_branch: 'trunk-content-dev',
            non_virtual_data_branch: 'trunk-content-dev'
        ],
        'trunk-code-dev-test'      : [
            code_folder            : 'mainline',
            code_branch            : 'trunk-code-dev-test',
            data_folder            : 'mainline',
            data_branch            : 'trunk-code-dev-test',
            non_virtual_code_branch: 'trunk-code-dev',
            non_virtual_data_branch: 'trunk-code-dev'
        ],
        'trunk-code-dev-clean'     : [
            code_folder            : 'mainline',
            code_branch            : 'trunk-code-dev-clean',
            data_folder            : 'mainline',
            data_branch            : 'trunk-code-dev-clean',
            non_virtual_code_branch: 'trunk-code-dev',
            non_virtual_data_branch: 'trunk-code-dev'
        ],
        // 'trunk-content-dev-metrics' : [
        //     code_folder            : 'mainline',
        //     code_branch            : 'trunk-content-dev-metrics',
        //     data_folder            : 'mainline',
        //     data_branch            : 'trunk-content-dev-metrics',
        //     non_virtual_code_branch: 'trunk-content-dev',
        //     non_virtual_data_branch: 'trunk-content-dev'
        // ],
        'criterion-content-warm'   : [
            code_folder            : 'mainline',
            code_branch            : 'criterion-content-warm',
            data_folder            : 'mainline',
            data_branch            : 'criterion-content-warm',
            non_virtual_code_branch: 'trunk-content-dev',
            non_virtual_data_branch: 'trunk-content-dev'
        ],
        'dev-na-to-trunk'          : [
            code_folder         : 'stage',
            code_branch         : 'dev-na-to-trunk',
            data_folder         : 'stage',
            data_branch         : 'dev-na-to-trunk',
            job_label_poolbuild : 'poolbuild_dev-na-to-trunk',
            job_label_statebuild: 'statebuild_dev-na-to-trunk',
        ],
        'dev-na-to-trunk-sub'      : [
            code_folder         : 'stage',
            code_branch         : 'dev-na-to-trunk-sub',
            data_folder         : 'stage',
            data_branch         : 'dev-na-to-trunk-sub',
            job_label_poolbuild : 'poolbuild_dev-na-to-trunk', // COBRA-6023
            job_label_statebuild: 'statebuild_dev-na-to-trunk', // COBRA-6023
        ],
        'trunk-to-dev-na'          : [
            code_folder         : 'stage',
            code_branch         : 'trunk-to-dev-na',
            data_folder         : 'stage',
            data_branch         : 'trunk-to-dev-na',
            job_label_poolbuild : 'poolbuild_trunk-to-dev-na',
            job_label_statebuild: 'statebuild_trunk-to-dev-na',
        ],
        'bf-playtest-sp'           : [
            code_folder            : 'mainline',
            code_branch            : 'trunk-code-dev',
            data_folder            : 'mainline',
            data_branch            : 'bf-playtest-sp',
            non_virtual_data_branch: 'trunk-content-dev'
        ],
        'media-team'               : [
            code_folder: 'tasks',
            code_branch: 'media-team',
            data_folder: 'tasks',
            data_branch: 'media-team',
        ],
        'task1'                    : [
            code_folder: 'tasks',
            code_branch: 'task1',
            data_folder: 'tasks',
            data_branch: 'task1',
        ],
        'task3'                    : [
            code_folder: 'tasks',
            code_branch: 'task3',
            data_folder: 'tasks',
            data_branch: 'task3',
        ],
    ]
    static Map preflight_branches = [:]
    static Map autotest_branches = [:]
    static Map integrate_branches = [
        'trunk-code-dev_to_trunk-content-dev-reworked'       : [
            asset                       : 'DevLevels',
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[trunk-code-dev]_to_[trunk-content-dev]_IgnoreData',
            job_label                   : 'trunk-code-dev_to_trunk-content-dev',
            integrate_upgrade_one_stream: true,
            local_upgrade               : true,
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'mainline',
            source_branch               : 'trunk-code-dev',
            timeout_hours               : 6,
            target_project              : project,
            target_folder               : 'mainline',
            target_branch               : 'trunk-content-dev',
            verified_integration        : true,
            get_integration_info        : true,
            integration_reference_job   : 'trunk-code-dev.autotest-to-integration.code',
            workspace_root              : project.workspace_root,
            freestyle_job_trigger_matrix: [
                [
                    upstream_job  : 'trunk-code-dev.integrate-upgrade-to.trunk-content-dev',
                    downstream_job: 'trunk-code-dev.integrate-upgrade-to.trunk-to-dev-na.start',
                    args          : ['code_changelist']
                ],
            ],
        ],
        'trunk-content-dev-to-trunk-code-dev-branch-guardian': [
            asset                       : 'DevLevels',
            branch_guardian             : true,
            disable_build               : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[trunk-content-dev]_to_[trunk-code-dev]_IgnoreData',
            integrate_upgrade_one_stream: true,
            job_label                   : 'trunk-content-dev-to-trunk-code-dev-branch-guardian',
            manual_trigger              : true,
            data_upgrade                : true,
            no_submit                   : false,
            preview_project             : project,
            preview_folder              : 'mainline',
            preview_branch              : 'trunk-content-dev',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'mainline',
            source_branch               : 'trunk-content-dev',
            target_project              : project,
            target_folder               : 'mainline',
            target_branch               : 'trunk-code-dev',
            timeout_hours               : 8,
            use_preview_dotnet_version  : false,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'trunk-code-dev-to-trunk-to-dev-na-branch-guardian'  : [
            asset                       : 'DevLevels',
            branch_guardian             : true,
            disable_build               : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[trunk-code-dev]_to_[trunk-to-dev-na]_OnlyLicensee',
            integrate_upgrade_one_stream: true,
            // integration_reference_job   : 'trunk-code-dev.autotest-to-integration.code',
            create_ref_job              : true,
            get_integration_info        : false,
            job_label                   : 'trunk-code-dev-to-trunk-to-dev-na-branch-guardian',
            manual_trigger              : true,
            no_submit                   : false,
            preview_project             : project,
            preview_folder              : 'mainline',
            preview_branch              : 'trunk-code-dev',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'mainline',
            source_branch               : 'trunk-code-dev',
            target_project              : project,
            target_folder               : 'stage',
            target_branch               : 'trunk-to-dev-na',
            timeout_hours               : 8,
            use_preview_dotnet_version  : false,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'task1-to-trunk-content-dev-branch-guardian'         : [
            asset                       : 'DevLevels',
            branch_guardian             : true,
            disable_build               : true,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[task1]_to_[trunk-content-dev]_IgnoreData',
            integrate_upgrade_one_stream: true,
            integration_reference_job   : 'task1.autotest-to-integration.code',
            job_label                   : 'task1-to-trunk-content-dev-branch-guardian',
            manual_trigger              : true,
            no_submit                   : true,
            preview_project             : project,
            preview_folder              : 'mainline',
            preview_branch              : 'trunk-content-dev',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'tasks',
            source_branch               : 'task1',
            target_project              : project,
            target_folder               : 'mainline',
            target_branch               : 'trunk-content-dev',
            timeout_hours               : 8,
            use_preview_dotnet_version  : false,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'trunk-to-dev-na-to-dev-na-upgrade-reworked'         : [
            asset                       : 'DevLevels',
            dataset_integration         : 'bfdata',
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[trunk-to-dev-na]_to_[dev-na]_OnlyLicensee',
            integrate_upgrade_one_stream: true,
            job_label                   : 'data-upgrade-trunk-to-dev-na-to-dev-na-upgrade',
            slack_channel               : '#bct-build-notify',
            slack_project               : project,
            source_project              : project,
            source_folder               : 'stage',
            source_branch               : 'trunk-to-dev-na',
            target_project              : Fb1Battlefieldgame,
            target_folder               : 'fbstream',
            target_branch               : 'dev-na-battlefield-integrations',
            timeout_hours               : 4,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
            integration_reference_job   : 'trunk-to-dev-na.autotest-to-integration.code',
            create_ref_job              : true,
            get_integration_info        : true,
            freestyle_job_trigger_matrix: [],
        ],
        'dev-na-to-trunk-to-dev-na-upgrade-reworked'         : [
            asset                       : 'DevLevels',
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[dev-na]_to_[trunk-to-dev-na]_OnlyLicensee',
            copy_mapping                : 'BF_[dev-na]_to_[trunk-to-dev-na]_OnlyEngine',
            local_upgrade               : true,
            integrate_upgrade_one_stream: true,
            job_label                   : 'data-upgrade-dev-na-to-trunk-to-dev-na-upgrade',
            slack_channel               : '#bf-trunk-to-dev-na-build-notify',
            source_project              : Fb1Battlefieldgame,
            source_folder               : 'fbstream',
            source_branch               : 'dev-na',
            target_project              : project,
            target_folder               : 'stage',
            target_branch               : 'trunk-to-dev-na',
            verified_integration        : true,
            workspace_root              : project.workspace_root,
            integration_reference_job   : 'dev-na.autotest-to-integration.code',
            create_ref_job              : true,
            get_integration_info        : true,
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map copy_branches = [
        'trunk-content-dev_to-task1': [
            source_folder               : 'mainline', source_branch: 'trunk-content-dev',
            target_folder               : 'tasks', target_branch: 'task1',
            code                        : true, data: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#trunk-content-dev-integrates-task1',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H * * * *',
            job_label_statebuild        : 'task1 && frosty',
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'trunk-code-dev': [
            code_folder                       : 'mainline', code_branch: 'trunk-code-dev',
            data_folder                       : 'mainline', data_branch: 'trunk-code-dev',
            include_vault                     : true,
            include_register_release_candidate: true,
        ],
    ]
    static List dashboard_list = [
        'trunk-code-dev',
    ]
    static Map dvcs_configs = [:
                               // 'trunk-code-dev-frostbite'  : [
                               //     project            : project,
                               //     branch_name        : 'trunk-code-dev',
                               //     slack_channel      : '#bct-build-notify',
                               //     slack_always_notify: true,
                               //     dry_run            : false,
                               //     trigger_type       : 'cron',
                               //     trigger_string     : 'H/5 * * * 1-6',
                               //     remote_spec        : 'dev-na-bf-data',
                               //     workspace_root     : project.workspace_root,
                               //     code_folder        : 'mainline',
                               //     code_branch        : 'trunk-code-dev',
                               //     data_folder        : 'mainline',
                               //     data_branch        : 'trunk-code-dev',
                               //     elipy_install_call : project.elipy_install_call,
                               //     elipy_call         : project.elipy_call,
                               //     job_label          : 'bct && dvcs',
                               //     skip_fetch         : true,
                               // ],
                               // 'bct-frostbite'       : [
                               //     project            : project,
                               //     branch_name        : 'trunk-code-dev',
                               //     slack_channel      : '#bct-build-notify',
                               //     slack_always_notify: true,
                               //     dry_run            : false,
                               //     trigger_type       : 'cron',
                               //     trigger_string     : 'H/5 * * * 1-6',
                               //     remote_spec        : 'DVCS-bf-code-mirror',
                               //     workspace_root     : project.workspace_root,
                               //     code_folder        : 'mainline',
                               //     code_branch        : 'trunk-code-dev',
                               //     data_folder        : 'mainline',
                               //     data_branch        : 'trunk-code-dev',
                               //     elipy_install_call : project.elipy_install_call,
                               //     elipy_call         : project.elipy_call,
                               //     job_label          : 'dvcs && bf-code-mirror',
                               //     p4_fb_settings     : [
                               //         p4_port               : 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
                               //         p4_creds              : 'perforce-p4buildedge02-fb-bct01',
                               //         p4_stream             : 'trunk-to-dev-na',
                               //         workspace_name_postfix: '-authenticate-${NODE_NAME}',
                               //         workspace_type        : 'manual',
                               //         view                  : '//dicestudio/battlefield/... //${P4_CLIENT}/dicestudio/battlefield/...',
                               //     ],
                               //     p4_data_server     : 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
                               //     p4_data_client_env : 'jenkins-%NODE_NAME%-codestream-authenticate',
                               // ],
    ]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
