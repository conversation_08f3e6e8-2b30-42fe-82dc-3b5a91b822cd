<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>node</name>
                    <description>Input Jenkins agent name which need to be put offline. e.g cu2-66ae33</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>reason</name>
                    <description>Describe why this node should be offline. e.g troubleshooting issue; test new hardware, [Taint] NeedsTainting </description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>21</daysToKeep>
        <numToKeep>20</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

import com.cloudbees.groovy.cps.NonCPS

/**
 * offlineAgent.groovy
 */
stage('KillSwitch') {
    timestamps {
        if ((!params.node) || (!params.reason)) {
            currentBuild.result = 'FAILURE'
            error('Require a valid node name and reason')
        } else {
            user = input submitterParameter: 'user', message: "Are you certain to take offline node: ${params.node}?"
            currentBuild.displayName = "${user}.${params.node}"
        }
    }
    node('master') {
        try {
            echo "Perform temperary offline on node ${params.node}"
            offlineNode(params.node, user + ': ' + params.reason)
        } catch (Exception e) {
            echo e.message
            throw e
        }
    }
}

@NonCPS
void offlineNode(String node, String reasons) {
    Jenkins.get().getComputer(node).setTemporarilyOffline(true, new hudson.slaves.OfflineCause.ByCLI(reasons))
    echo node
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>