<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Trigger a manual full garbage collection on the <PERSON> master to clear up metaspace, unused classes and heap space.</description>
    <keepDependencies>false</keepDependencies>
    <properties/>
    <scm class="hudson.scm.NullSCM"/>
    <canRoam>false</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers>
        <hudson.triggers.TimerTrigger>
            <spec>H * * * 1-6
H 6-23 * * 7</spec>
        </hudson.triggers.TimerTrigger>
    </triggers>
    <concurrentBuild>false</concurrentBuild>
    <builders>
        <hudson.plugins.groovy.SystemGroovy>
            <bindings/>
            <source class="hudson.plugins.groovy.StringSystemScriptSource">
                <script>
                    <script>System.gc()</script>
                    <sandbox>true</sandbox>
                    <classpath/>
                </script>
            </source>
        </hudson.plugins.groovy.SystemGroovy>
    </builders>
    <publishers/>
    <buildWrappers>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <hudson.plugins.build__timeout.BuildTimeoutWrapper>
            <strategy class="hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy">
                <timeoutMinutes>1</timeoutMinutes>
            </strategy>
            <operationList>
                <hudson.plugins.build__timeout.operations.AbortOperation/>
                <hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
                    <description>Build aborted due to timeout after {0} minutes</description>
                </hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
            </operationList>
        </hudson.plugins.build__timeout.BuildTimeoutWrapper>
    </buildWrappers>
    <logRotator>
        <daysToKeep>10</daysToKeep>
        <numToKeep>50</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <assignedNode>master</assignedNode>
</project>