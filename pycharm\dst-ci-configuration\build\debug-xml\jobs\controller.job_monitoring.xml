<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Maintenance job that alerts when jobs get stuck</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>@hourly</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>SLACK_CHANNEL=#cobra-jobdsl
PROJECT_SHORT_NAME=bct
CLOUD_NODE_PREFIX=bct6-</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>50</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.lib.LibSlack
import hudson.console.ModelHyperlinkNote
import hudson.model.Queue
import hudson.model.queue.CauseOfBlockage

/**
 * JobMonitoringScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Determine if job fails to provision an agent') {
            steps {
                script {
                    String slackChannel = env.SLACK_CHANNEL
                    String projectShortName = env.PROJECT_SHORT_NAME
                    String cloudNodePrefix = env.CLOUD_NODE_PREFIX
                    StringJoiner log = new StringJoiner('\n')
                    StringJoiner slackMessage = new StringJoiner('\n')
                    slackMessage.add("A &lt;${env.BUILD_URL}console|maintenance job&gt; has found stuck jobs:")
                    boolean sendMessage = false
                    int maxCloudVmLimit = LibJenkins.maxCloudVirtualMachinesLimit
                    log.add("Maximum number of cloud VMs: $maxCloudVmLimit")
                    def cloudAgents = LibJenkins.getNodesWithPrefix(cloudNodePrefix)
                    int buildingCloudAgentsCount = cloudAgents.findAll { !it.toComputer().idle }.size()
                    log.add("Cloud agents currently building: ${buildingCloudAgentsCount}")
                    boolean spaceInQueue = maxCloudVmLimit - buildingCloudAgentsCount &gt; 0
                    log.add("There is space in cloud queue: $spaceInQueue")
                    echo log.toString()
                    LibJenkins.jobsInQueue
                        .findAll { Queue.Item item -&gt; item.stuck }
                        .each { Queue.Item item -&gt;
                            CauseOfBlockage blockage = item.causeOfBlockage
                            Queue.Task task = item.task
                            echo "${ModelHyperlinkNote.encodeTo("/${task.url}", task.fullDisplayName)} has " +
                                "been stuck since ${new Date(item.inQueueSince)} for ${item.inQueueForString}. ${blockage.shortDescription}." +
                                "\n${blockage.class}"
                            if (task.fullDisplayName.indexOf('avalanche_maintenance') == -1) {
                                sendMessage = true
                                slackMessage.add("- &lt;${LibJenkins.rootUrl}${task.url}|${task.fullDisplayName}&gt; has been stuck " +
                                    "for ${item.inQueueForString}. ${blockage.shortDescription}. ${blockage.class}")
                            }
                        }
                    if (sendMessage) {
                        echo 'Sending slack message'
                        String color = 'danger'
                        LibSlack.sendMessage(this, slackChannel, slackMessage.toString(), projectShortName, color)
                        echo 'Message sent. Done.'
                    } else {
                        echo 'All is good. No stuck jobs.'
                    }
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>