<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Look at not running jobs and see if they are failing with certain strings in their log and then restart the nodes.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>H * * * 1-5</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>50</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

import hudson.model.Job
import hudson.model.Result
import jenkins.model.Jenkins

/**
 * restartAgentOnFailure.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Run job to detect broken nodes that need a restart') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER
                    List&lt;Job&gt; failedJobs = []
                    List&lt;String&gt; failureStrings = [
                        'file oo2core_8_win64.dll operating system write error',
                        'HTTP Error 503: Service Unavailable; periodic maintenance',
                        'Failed to obtain a lease from \'localhost\': offline',
                    ]
                    echo('Ignoring running jobs...')
                    List&lt;Job&gt; jobs = Jenkins.get().getItems(Job).findAll { job -&gt;
                        !job.building
                    }

                    echo('Processing jobs...')
                    for (def job : jobs) {
                        try {
                            echo("\tProcessing ${job.name}")
                            def build = job.lastCompletedBuild
                            def buildLog = build?.result == Result.FAILURE ? build.getLog(200) : ['']
                            def failureStringFound = buildLog.any { line -&gt;
                                failureStrings.any { failureString -&gt;
                                    line.contains(failureString)
                                }
                            }

                            if (failureStringFound) {
                                failedJobs.add(job)
                            }
                        } catch (FileNotFoundException exc) {
                            echo("No log found for ${job.name}, skipping")
                        }
                    }

                    echo("Found ${failedJobs.size()} failed builds, restarting nodes")
                    List&lt;String&gt; buildNodeNames = []
                    failedJobs.each { failedJob -&gt;
                        // codenarc-disable UnnecessaryGetter
                        buildNodeNames.add(failedJob.getLastBuiltOn().name)
                    }
                    buildNodeNames.unique()
                    buildNodeNames.each {
                        echo "Build recently failed for ${it}"
                    }

                    List&lt;String&gt; recentlyRebooted = []
                    def restartProject = Jenkins.get().getItemByFullName('agent.reboot')
                    Long HOUR = 60 * 60 * 1000
                    Date endDate = new Date()
                    Date startDate = new Date(endDate.toInstant().toEpochMilli() - (HOUR * 2))
                    // codenarc-disable UnnecessaryGetter
                    List&lt;Job&gt; restartJobs = restartProject.getBuildsByTimestamp(startDate.getTime(), endDate.getTime())
                    restartJobs.each { job -&gt;
                        String nodeName = job.getBuiltOn().name
                        echo "Skipping recently rebooted node: $nodeName"
                        recentlyRebooted.add(nodeName)
                    }

                    buildNodeNames = buildNodeNames.findAll { nodeName -&gt;
                        !recentlyRebooted.contains(nodeName)
                    }

                    buildNodeNames.each { buildNodeName -&gt;
                        echo "Triggering agent.reboot job on ${buildNodeName}"
                        build wait: false, propagate: false, job: 'agent.reboot', parameters: [
                            [
                                $class         : 'NodeParameterValue',
                                labels         : ["$buildNodeName"],
                                name           : 'machine',
                                nodeEligibility: [$class: 'AllNodeEligibility']
                            ]
                        ]
                    }

                    if (failedJobs) {
                        currentBuild.result = 'UNSTABLE'
                    }
                }
                SlackMessageNew(currentBuild, '#cobra-support-alerts', 'cob')
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>