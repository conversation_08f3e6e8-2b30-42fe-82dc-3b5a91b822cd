<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Runs arbitrary command on the specified machine via BFA automation.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <org.jvnet.jenkins.plugins.nodelabelparameter.NodeParameterDefinition>
                    <name>machine</name>
                    <description>select which machine to run command on</description>
                    <allowedSlaves/>
                    <defaultSlaves/>
                    <triggerIfResult>allowMultiSelectionForConcurrentBuilds</triggerIfResult>
                    <allowMultiNodeSelection>true</allowMultiNodeSelection>
                    <triggerConcurrentBuilds>true</triggerConcurrentBuilds>
                    <ignoreOfflineNodes>false</ignoreOfflineNodes>
                    <nodeEligibility class="org.jvnet.jenkins.plugins.nodelabelparameter.node.IgnoreOfflineNodeEligibility"/>
                </org.jvnet.jenkins.plugins.nodelabelparameter.NodeParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>command</name>
                    <description>Command to execute on the target machine</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>1</maxConcurrentPerNode>
            <maxConcurrentTotal>0</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
    </properties>
    <canRoam>true</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers/>
    <concurrentBuild>true</concurrentBuild>
    <builders>
        <hudson.tasks.BatchFile>
            <command>${command}</command>
        </hudson.tasks.BatchFile>
    </builders>
    <publishers/>
    <buildWrappers>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
            <template>${JOB_NAME}.${machine}</template>
        </org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
    </buildWrappers>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <scm class="hudson.plugins.git.GitSCM">
        <userRemoteConfigs>
            <hudson.plugins.git.UserRemoteConfig>
                <name>origin</name>
                <url>*****************:dre-cobra/dst-ci-configuration.git</url>
                <credentialsId>monkey-commons-ssh-v2</credentialsId>
            </hudson.plugins.git.UserRemoteConfig>
        </userRemoteConfigs>
        <branches>
            <hudson.plugins.git.BranchSpec>
                <name>master</name>
            </hudson.plugins.git.BranchSpec>
        </branches>
        <configVersion>2</configVersion>
        <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
        <gitTool>Default</gitTool>
        <extensions>
            <hudson.plugins.git.extensions.impl.CloneOption>
                <shallow>true</shallow>
                <noTags>true</noTags>
                <reference/>
                <timeout>20</timeout>
                <honorRefspec>false</honorRefspec>
                <depth>0</depth>
            </hudson.plugins.git.extensions.impl.CloneOption>
            <hudson.plugins.git.extensions.impl.PathRestriction plugin="git@5.2.1">
                <includedRegions/>
                <excludedRegions>.*</excludedRegions>
            </hudson.plugins.git.extensions.impl.PathRestriction>
            <hudson.plugins.git.extensions.impl.RelativeTargetDirectory>
                <relativeTargetDir>ci</relativeTargetDir>
            </hudson.plugins.git.extensions.impl.RelativeTargetDirectory>
        </extensions>
        <browser class="hudson.plugins.git.browser.GitLab">
            <url>https://gitlab.ea.com/</url>
            <version>13.9</version>
        </browser>
    </scm>
</project>