<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Triggers vaulting for build and symbols.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_branch</name>
                    <description>Code branch</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_branch</name>
                    <description>Data branch</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Code changelist</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Data changelist</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>version</name>
                    <description>Version of build (1.0)</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>build_location</name>
                    <description>Build location to vault. Default is in Stockholm datacenter. For UK-based builds, use Guildford</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>vault_verification_location</name>
                    <description>Elipy location for the vault verification settings. For default settings, leave empty. For other settings, specify location.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>additional_baseline_locations</name>
                    <description>Store baseline in additional locations (default is the build location). Available options: DiceStockholm/Guildford/RippleEffect, comma separated list for multiple options.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>vault_win64_trial</name>
                    <description>Include win64-trial platform on symbol</description>
                    <defaultValue>false</defaultValue>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>verify_post_vault</name>
                    <description>Verify files post build vault</description>
                    <defaultValue>true</defaultValue>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>linuxserver</name>
                    <defaultValue>true</defaultValue>
                    <description>Include linuxserver in the vault process.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>ps4</name>
                    <defaultValue>false</defaultValue>
                    <description>Include ps4 in the vault process.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>ps5</name>
                    <defaultValue>true</defaultValue>
                    <description>Include ps5 in the vault process.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>server</name>
                    <defaultValue>true</defaultValue>
                    <description>Include server in the vault process.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>win64</name>
                    <defaultValue>true</defaultValue>
                    <description>Include win64 in the vault process.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>xb1</name>
                    <defaultValue>false</defaultValue>
                    <description>Include xb1 in the vault process.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>xbsx</name>
                    <defaultValue>true</defaultValue>
                    <description>Include xbsx in the vault process.</description>
                </hudson.model.BooleanParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>project_name=merlin</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.maintenance

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def project = ProjectClass(env.project_name)

/**
 * vault_pipeline.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Vaulting jobs') {
            steps {
                script {
                    def args = [
                        string(name: 'code_branch', value: params.code_branch),
                        string(name: 'data_branch', value: params.data_branch),
                        string(name: 'code_changelist', value: params.code_changelist),
                        string(name: 'data_changelist', value: params.data_changelist),
                        string(name: 'version', value: params.version),
                        string(name: 'build_location', value: params.build_location),
                        string(name: 'vault_verification_location', value: params.vault_verification_location),
                        string(name: 'additional_baseline_locations', value: params.additional_baseline_locations),
                    ]
                    def jobs = [:]

                    currentBuild.displayName = "${env.JOB_NAME}.${params.data_changelist}.${params.code_changelist}"
                    def final_result = Result.SUCCESS
                    def branchfile = GetBranchFile.get_branchfile(project.name, params.data_branch)
                    def expression_debug_data = branchfile.standard_jobs_settings.expression_debug_data ? ' --expression-debug-data' : ''
                    args += [string(name: 'expression_debug_data', value: expression_debug_data)]

                    def vault_win64_trial = params.vault_win64_trial ? ' --win64-trial' : ' --no-win64-trial'
                    args += [string(name: 'vault_win64_trial', value: vault_win64_trial)]

                    def verify_post_vault = params.verify_post_vault ? ' --verify-post-vault' : ''
                    args += [string(name: 'verify_post_vault', value: verify_post_vault)]

                    ['build'].each { job_type -&gt;
                        LibCommonCps.VAULT_PLATFORMS.each { platform -&gt;
                            if (params[platform]) {
                                jobs[platform + job_type] = {
                                    def downstream_job = build(
                                        job: "vault.${project.name}.${project.short_name}.${job_type}",
                                        parameters: args + [string(name: 'platform', value: platform)],
                                        propagate: false,
                                    )
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }
                    }

                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    SlackMessageNew(currentBuild, '#cobra-outage-vault', project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>180</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>