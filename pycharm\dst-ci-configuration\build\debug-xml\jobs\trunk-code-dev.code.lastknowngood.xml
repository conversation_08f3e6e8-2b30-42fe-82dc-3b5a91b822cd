<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Get latest code changelist.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Code changelist used for latest successful code build on null</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <scm class="hudson.scm.NullSCM"/>
    <canRoam>false</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers/>
    <concurrentBuild>false</concurrentBuild>
    <builders/>
    <publishers>
        <jenkins.plugins.slack.SlackNotifier plugin="slack@761.v2a_8770f0d169">
            <teamDomain>electronic-arts</teamDomain>
            <tokenCredentialId>bct-slack-token</tokenCredentialId>
            <botUser>false</botUser>
            <room>#cobra-build-preflight</room>
            <sendAsText>false</sendAsText>
            <startNotification>false</startNotification>
            <notifySuccess>false</notifySuccess>
            <notifyAborted>true</notifyAborted>
            <notifyNotBuilt>false</notifyNotBuilt>
            <notifyUnstable>false</notifyUnstable>
            <notifyRegression>false</notifyRegression>
            <notifyFailure>true</notifyFailure>
            <notifyEveryFailure>false</notifyEveryFailure>
            <notifyBackToNormal>true</notifyBackToNormal>
            <notifyRepeatedFailure>true</notifyRepeatedFailure>
            <includeTestSummary>false</includeTestSummary>
            <includeFailedTests>false</includeFailedTests>
            <uploadFiles>false</uploadFiles>
            <commitInfoChoice>NONE</commitInfoChoice>
            <includeCustomMessage>false</includeCustomMessage>
        </jenkins.plugins.slack.SlackNotifier>
    </publishers>
    <buildWrappers>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
            <template>${JOB_NAME}.${code_changelist}</template>
        </org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
    </buildWrappers>
    <assignedNode>master</assignedNode>
    <logRotator>
        <daysToKeep>30</daysToKeep>
        <numToKeep>200</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <authToken>remotebuild</authToken>
</project>