<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Sync CH1-code-dev code and deploy a binary build.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>BRANCH_NAME=CH1-code-dev
CODE_BRANCH=CH1-code-dev
CODE_FOLDER=CH1
PROJECT_NAME=bctch1
NON_VIRTUAL_CODE_BRANCH=
NON_VIRTUAL_CODE_FOLDER=</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>CODE_CHANGELIST</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>CLEAN_LOCAL</name>
                    <defaultValue>false</defaultValue>
                    <description>If true, TnT/Local will be deleted at the beginning of the run.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>SUBMIT</name>
                    <defaultValue>true</defaultValue>
                    <description>Submit the result to perforce. Uncheck this if you want dry-run</description>
                </hudson.model.BooleanParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.SCMTrigger>
                    <spec>H/2 * * * 1-6
H/2 6-23 * * 7</spec>
                    <ignorePostCommitHooks>false</ignorePostCommitHooks>
                </hudson.triggers.SCMTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.gametool

import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJenkins
import com.ea.lib.LibPerforce
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchFile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def branchInfo = branchFile.general_settings + branchFile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * GametoolScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    String p4CodeRoot = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_root', '', project)
                    String p4CodeCredentials = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_creds', '', project)
                    boolean isTestEnvironment = env.PRODUCTION == 'False'
                    List&lt;String&gt; gametools = []
                    branchInfo.gametool_settings.gametools.each { String gametool, Map value -&gt;
                        gametools &lt;&lt; gametool
                    }
                    LibPerforce libPerforce = new LibPerforce(this, project.short_name, gametools,
                        env.CODE_FOLDER, env.CODE_BRANCH, p4CodeRoot, p4CodeCredentials, env.JOB_NAME,
                        isTestEnvironment, env.NON_VIRTUAL_CODE_FOLDER, env.NON_VIRTUAL_CODE_BRANCH)
                    libPerforce.setPollScmTriggers()
                    String codeChangelist = params.CODE_CHANGELIST ?: env.P4_CHANGELIST
                    List&lt;String&gt; modifiedTools = libPerforce.getModifiedTools(codeChangelist)
                    Map&lt;String, String&gt; injectMap = [
                        CODE_CHANGELIST   : codeChangelist,
                        GAMETOOLS_TO_BUILD: modifiedTools.inspect(),
                    ]
                    EnvInject(currentBuild, injectMap)
                }
            }
        }
        stage('Trigger Gametool Build job') {
            steps {
                script {
                    List&lt;JobReference&gt; jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        def codeChangelist = env.CODE_CHANGELIST
                        def cleanLocal = params.CLEAN_LOCAL
                        List&lt;String&gt; gametoolsToBuild = (List&lt;String&gt;) Eval.me(env.GAMETOOLS_TO_BUILD)

                        def args = [
                            string(name: 'CODE_CHANGELIST', value: codeChangelist),
                            booleanParam(name: 'CLEAN_LOCAL', value: cleanLocal),
                        ]

                        def injectMap = [
                            'code_changelist': codeChangelist,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = "${env.JOB_NAME}.${codeChangelist}"
                        Map jobs = [:]
                        gametoolsToBuild.each { String gametool -&gt;
                            jobs[gametool] = {
                                String jobName = "${env.BRANCH_NAME}.gametool.${gametool}"
                                def downstreamJob = build(job: jobName, parameters: args, propagate: false)
                                jobReferences &lt;&lt; new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: args, propagate: false)
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                        parallel(jobs)

                        def slackSettings = branchInfo.gametool_settings.slack_channel
                        SlackMessageNew(currentBuild, slackSettings, project.short_name)
                    }
                }
            }
        }
    }
}

String toString() {
    return 'scripts.schedulers.gametool.GametoolScheduler'
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>