COBRA-7372: Combined Bundles Jenkins Job Seed Rerun Instructions

1. Ensure the updated `basic_jobs.groovy` is committed and pushed to the `dst-ci-configuration` repository.
2. Trigger the Jenkins seed job for the Battlefield CI pipeline (usually named `all.basic_jobs` or similar).
   - This can be done via the Jenkins UI or CLI, depending on your setup.
3. Monitor the seed job execution and confirm that jobs named `<branch>.combined_bundles.<platform>` (e.g., `CH1-content-dev-disc-build.combined_bundles.ps5`) are created for all relevant platforms.
4. Verify that the job configuration matches the expected output path and parameters for combine bundles.
5. Run the new job(s) and confirm output is produced to the filer as expected.
6. Update stakeholders and documentation with the new job naming and logic.

If any issues arise, review the Jenkins seed job logs and the updated Groovy logic for errors or misconfigurations.
