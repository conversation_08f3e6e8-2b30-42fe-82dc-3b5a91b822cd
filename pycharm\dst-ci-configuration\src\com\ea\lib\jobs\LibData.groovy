package com.ea.lib.jobs

import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl

class LibData {
    /**
     * Adds generic job parameters for data start jobs.
     */
    static void data_start(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['data']
        def data_reference_job = branch_info.data_reference_job ?: branch_info.code_branch + '.code.start'
        def deployment_data_reference_job = branch_info.deployment_data_reference_job ?: data_reference_job
        def disable_build = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'disable_build', false)
        def dry_run_data = branch_info.dry_run_data ?: false
        def export_data = branch_info.export_data ?: false
        def export_data_reference_job = branch_info.export_data_reference_job ?: branch_info.code_branch + '.code.start'
        def extra_data_path = branch_info.extra_data_path ?: ''
        def enable_daily_data_clean = branch_info.enable_daily_data_clean ?: ''
        def enable_lkg_cleaning = branch_info.enable_lkg_cleaning ?: ''
        def main_unverified_branch = branch_info.main_unverified_branch ?: false
        def non_virtual_data_branch = branch_info.non_virtual_data_branch ?: ''
        def non_virtual_data_folder = branch_info.non_virtual_data_folder ?: ''
        def retry_limit = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'retry_limit', 0, project)
        def skip_frosty_scheduler = branch_info.skip_frosty_scheduler ?: branch_info.skip_frosty_trigger ?: false
        def slack_notify_bot_data = branch_info.slack_notify_bot_data ?: false
        def trigger_string = branch_info.trigger_string_data ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
        def verified_data = branch_info.verified_data ?: false
        def verified_data_reference_job = branch_info.verified_data_reference_job ?: branch_info.branch_name + '.future-smoke.copy-up-from.' + branch_info.branch_name + '-unverified'
        def webexport_branch = branch_info.webexport_branch ?: false
        def webexport_allow_failure = branch_info.webexport_allow_failure ?: false
        def frostbite_syncer_setup = project.frostbite_syncer_setup ?: false
        def clean_build_validation = branch_info.clean_build_validation ?: false
        def trigger_string_clean_build_validation = branch_info.trigger_string_clean_build_validation ?: 'H 3 * * 1-6'
        def verify_for_preflight = branch_info.verify_for_preflight ?: false

        def description_string = 'Builds data for ' + branch_info.branch_name + ' on all platforms.'
        def trigger_type = branch_info.trigger_type_data ?: 'scm'
        if (export_data == true) {
            description_string = 'Builds and exports data for ' + branch_info.branch_name + ' on all platforms.'
        } else if (verified_data == true) {
            description_string = 'Builds data for ' + branch_info.branch_name + ' on all platforms, using the latest verified binary build.'
        } else if (frostbite_syncer_setup == true) {
            trigger_type = branch_info.trigger_type_data ?: 'none'
        }

        if (clean_build_validation == true) {
            description_string = 'Builds clean data validation for ' + branch_info.branch_name + ' on specified platforms.'
            trigger_type = 'cron'
            trigger_string = trigger_string_clean_build_validation
        }

        def clean_data_default = ['False', 'True']
        if (LibCommonNonCps.get_setting_value(branch_info, modifiers, 'clean_data', false, project) == true) {
            clean_data_default = clean_data_default.reverse()
        }

        def enable_lkg_p4_counters = branch_info.enable_lkg_p4_counters ?: false
        def no_lkg_patch_data = branch_info.no_lkg_patch_data ?: false

        // Add sections to the Jenkins job.
        job.with {
            description(description_string)
            disabled(disable_build)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (job.name.contains('deployment') == false) {
                            if (trigger_type == 'scm') {
                                pollSCM {
                                    scmpoll_spec(trigger_string)
                                }
                            } else if (trigger_type == 'cron') {
                                cron {
                                    spec(trigger_string)
                                }
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', clean_data_default, 'If True, Avalanche will be cleaned at the beginning of the run.')
            }
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('code_branch', branch_info.code_branch)
                env('data_branch', branch_info.data_branch)
                env('data_folder', branch_info.data_folder)
                env('data_reference_job', data_reference_job)
                env('datapreflight_info_sending', branch_info.datapreflight_info_sending)
                env('dataset', branch_info.dataset)
                env('deployment_data_reference_job', deployment_data_reference_job)
                env('dry_run_data', dry_run_data)
                env('export_data_reference_job', export_data_reference_job)
                env('extra_data_path', extra_data_path)
                env('frostbite_syncer_setup', frostbite_syncer_setup)
                env('main_unverified_branch', main_unverified_branch)
                env('non_virtual_data_branch', non_virtual_data_branch)
                env('non_virtual_data_folder', non_virtual_data_folder)
                env('project_name', project.name)
                env('retry_limit', retry_limit)
                env('skip_frosty_scheduler', skip_frosty_scheduler)
                env('slack_notify_bot_data', slack_notify_bot_data)
                env('verified_data_reference_job', verified_data_reference_job)
                env('webexport_branch', webexport_branch)
                env('webexport_allow_failure', webexport_allow_failure)
                env('enable_lkg_p4_counters', enable_lkg_p4_counters)
                env('enable_lkg_cleaning', enable_lkg_cleaning)
                env('no_lkg_patch_data', no_lkg_patch_data)
                env('enable_daily_data_clean', enable_daily_data_clean)
                env('verify_for_preflight', verify_for_preflight)
            }
        }
    }

    /**
     * Adds generic job parameters for patchdata start jobs.
     */
    static void patchdata_start(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['patchdata']
        def data_reference_job = branch_info.patchdata_reference_job ?: branch_info.data_reference_job ?: branch_info.code_branch + '.code.start'
        def data_triggers_patchdata = branch_info.data_triggers_patchdata ?: false
        def extra_data_path = branch_info.extra_data_path ?: ''
        def main_unverified_branch = branch_info.main_unverified_branch ?: false
        def non_virtual_data_branch = branch_info.non_virtual_data_branch ?: ''
        def non_virtual_data_folder = branch_info.non_virtual_data_folder ?: ''
        def patch_branch = branch_info.patch_branch ?: branch_info.data_branch
        def retry_limit = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'retry_limit', 0, project)
        def trigger_string = branch_info.trigger_string_patchdata ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
        def enable_lkg_p4_counters = branch_info.enable_lkg_p4_counters ?: false
        def enable_daily_data_clean = branch_info.enable_daily_data_clean ?: ''
        def enable_lkg_cleaning = branch_info.enable_lkg_cleaning ?: ''

        def frostbite_syncer_setup = project.frostbite_syncer_setup ?: false

        def trigger_type = branch_info.trigger_type_patchdata ?: 'scm'
        if (frostbite_syncer_setup == true) {
            trigger_type = branch_info.trigger_type_patchdata ?: 'none'
        }

        def clean_data_default = ['False', 'True']
        if (LibCommonNonCps.get_setting_value(branch_info, modifiers, 'clean_data', false, project) == true) {
            clean_data_default = clean_data_default.reverse()
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Builds data patches for ' + branch_info.branch_name + ' on all platforms.')
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (trigger_type == 'scm') {
                            pollSCM {
                                scmpoll_spec(trigger_string)
                            }
                        } else if (trigger_type == 'cron') {
                            cron {
                                spec(trigger_string)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', clean_data_default, 'If True, Avalanche will be cleaned at the beginning of the run.')
            }
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('code_branch', branch_info.code_branch)
                env('data_branch', branch_info.data_branch)
                env('data_folder', branch_info.data_folder)
                env('data_reference_job', data_reference_job)
                env('data_triggers_patchdata', data_triggers_patchdata)
                env('dataset', branch_info.dataset)
                env('extra_data_path', extra_data_path)
                env('frostbite_syncer_setup', frostbite_syncer_setup)
                env('main_unverified_branch', main_unverified_branch)
                env('non_virtual_data_branch', non_virtual_data_branch)
                env('non_virtual_data_folder', non_virtual_data_folder)
                env('patch_branch', patch_branch)
                env('project_name', project.name)
                env('retry_limit', retry_limit)
                env('enable_lkg_p4_counters', enable_lkg_p4_counters)
                env('enable_lkg_cleaning', enable_lkg_cleaning)
                env('enable_daily_data_clean', enable_daily_data_clean)
            }
        }
    }

    /**
     * Adds generic job parameters for data upgrade job.
     */
    static void upgrade_data(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['upgrade_data']
        def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)
        def frostbite_licensee = branch_info.frostbite_licensee
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        if (branch_info.statebuild_data == false) {
            job_label = branch_info.data_branch + ' && data'
        }
        if (branch_info.upgrade_data_dedicated_machine) {
            job_label = branch_info.data_branch + ' && data-upgrade'
        }
        def no_submit = branch_info.no_submit_upgrade_data
        def upgrade_data_submit_folder = branch_info.upgrade_data_submit_folder
        def upgrade_data_batch_file = branch_info.upgrade_data_batch_file

        def timeout_hours = branch_info.timeout_hours_upgrade_data ?: 4
        def timeout_minutes = timeout_hours * 60
        def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_fb_settings', [:], project)
        String p4_data_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_server', '', project)
        String p4_data_client_env = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_client_env', '', project)

        def extra_args = branch_info.extra_data_upgrade_args ?: ''
        if (user_credentials != '') {
            extra_args += ' --email %monkey_email% --password "%monkey_passwd%"'
        }
        if (frostbite_licensee != null) {
            extra_args += ' --licensee ' + frostbite_licensee
        }
        if (no_submit == true) {
            extra_args += ' --no-submit'
        }
        if (upgrade_data_submit_folder != null) {
            extra_args += ' --submit-folder ' + upgrade_data_submit_folder
        }
        if (upgrade_data_batch_file != null) {
            extra_args += ' --batch-file ' + upgrade_data_batch_file
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Runs data upgrade for ' + branch_info.data_branch + ' using code from ' + branch_info.code_branch + '.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                choiceParam('clean', ['False', 'True'], 'If True, tnt/local will be cleaned before run.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="P4_CHANGELIST"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' data_upgrade --data-directory ' + branch_info.dataset +
                    ' --code-branch ' + branch_info.code_branch + ' --code-changelist %code_changelist%' +
                    ' --data-changelist %P4_CHANGELIST%' +
                    ' --p4-port ' + p4_data_server + ' --p4-client ' + p4_data_client_env + ' --p4-user %P4_USER%' +
                    ' --clean %clean% ' + extra_args)
            }
            if (branch_info.upgrade_data_downstream_jobs != null) {
                publishers {
                    for (downstreamJobName in branch_info.upgrade_data_downstream_jobs.split(',')) {
                        downstreamParameterized {
                            trigger(downstreamJobName) {
                                triggerWithNoParameters(true)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds generic job parameters for data build jobs (including export-data build jobs, these have export_avalanche_state set to true).
     */
    static void data_job(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = [branch_info.platform]
        def is_export_data_job = branch_info.export_data ?: false
        def is_verified_data_job = branch_info.verified_data ?: false
        def compress_bundles = branch_info.avalanche_compress_bundles ?: false
        def no_trim = branch_info.no_trim != null ? branch_info.no_trim : true
        def frostbite_licensee = branch_info.frostbite_licensee
        def extra_args_list = branch_info.extra_data_args ?: []
        def extra_args = extra_args_list.join(' ')
        def timeout_hours = branch_info.timeout_hours_data ?: 3
        def use_recompression_cache = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'use_recompression_cache', false, project)
        def clean_master_version_check = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'clean_master_version_check', false, project)
        def expression_debug_data = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'expression_debug_data', false, project)
        def use_super_bundles = branch_info.use_super_bundles ?: false
        def is_clean_build_validation = branch_info.clean_build_validation ?: false
        def properties_file = LibCommonNonCps.get_setting_value(branch_info, [], 'properties_file', '', project)
        def custom_tag = branch_info?.custom_tag
        def is_virtual_stream = LibCommonNonCps.get_setting_value(branch_info, [], 'is_virtual_stream', false)
        def content_layers = branch_info.content_layers ?: []
        def include_default_layer = branch_info.include_default_layer ?: true

        if (branch_info.patch_data_platform) {
            use_super_bundles = false
        } else if (use_super_bundles == true && !is_export_data_job && branch_info.platform != 'server') {
            extra_args += ' --export-super-bundles true'
        }

        if (branch_info.export_combine_bundles) {
            extra_args += ' --export-combine-bundles true'
        }

        if (custom_tag != null) {
            extra_args += " --custom-tag ${custom_tag}"
        }

        def export_avalanche_state = branch_info.export_avalanche_state ?: false
        def import_avalanche = branch_info.import_avalanche_state ?: false
        def export_data_branch = branch_info.export_data_branch ?: false
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        String poolbuild_label = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'poolbuild_label', 'poolbuild', project)
        // Check for job_label_poolbuild in master settings
        String job_label_poolbuild = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'job_label_poolbuild', null, project)

        if (is_export_data_job) {
            export_avalanche_state = true
            timeout_hours = branch_info.timeout_hours_export_data ?: timeout_hours
            def poolbuild_export_data = branch_info.poolbuild_export_data ?: branch_info.poolbuild_data ?: false
            if (poolbuild_export_data == true) {
                // Use job_label_poolbuild if defined, otherwise use poolbuild_label
                job_label = job_label_poolbuild ?: poolbuild_label
                job_label += ' && ' + branch_info.platform
            }
        } else {
            if (branch_info.poolbuild_data == true) {
                // Use job_label_poolbuild if defined, otherwise use poolbuild_label
                job_label = job_label_poolbuild ?: poolbuild_label
                job_label += ' && ' + branch_info.platform
                if (!export_data_branch) {
                    import_avalanche = false
                }
            } else if (is_verified_data_job == false && branch_info.statebuild_data == false) {
                import_avalanche = false
                job_label = branch_info.data_branch + ' && data && ' + branch_info.platform + ' && !frosty'
            } else if (is_verified_data_job && branch_info.statebuild_verified_data == false) {
                import_avalanche = false
                job_label = branch_info.data_branch + ' && verified-data && ' + branch_info.platform
            }
            // Allow Enlighten bake jobs to use more build nodes
            if (branch_info.is_enlighten_job == true) {
                timeout_hours = branch_info.timeout_hours_enlighten_job ?: timeout_hours
                job_label = poolbuild_label
            }
        }

        if (is_clean_build_validation) {
            timeout_hours = branch_info.timeout_hours_clean_data_validation ?: timeout_hours
            job_label = branch_info.clean_build_validation_job_label ?: 'statebuild'
        }

        // 'validate-frosted' vms should share the win64 vms
        job_label = job_label.replace('validate-frosted', 'win64')

        def timeout_minutes = timeout_hours * 60

        def asset = branch_info.asset
        if (branch_info.platform == 'server') {
            asset = branch_info.server_asset
        }

        if (export_avalanche_state == true) {
            extra_args += " --export-avalanche-state ${export_avalanche_state}"
        }
        if (import_avalanche == true) {
            extra_args += " --import-avalanche-state ${import_avalanche}"
        }
        if (compress_bundles == true) {
            extra_args += " --enable-compression ${compress_bundles}"
        }
        if (frostbite_licensee != null) {
            extra_args += ' --licensee ' + frostbite_licensee
        }
        if (use_recompression_cache == true) {
            extra_args += " --use-recompression-cache ${use_recompression_cache}"
        }
        if (properties_file) {
            extra_args += ' --properties-file ' + properties_file
        }
        if (is_virtual_stream) {
            extra_args += ' --virtual-branch-override true '
        }
        for (layer in content_layers) {
            extra_args += ' --content-layers ' + layer
        }
        if (include_default_layer == false) {
            extra_args += ' --include-default-layer false '
        }
        if (clean_master_version_check == true) {
            extra_args += " --clean-master-version-check ${clean_master_version_check}"
        }
        if (expression_debug_data == true) {
            extra_args += " --expression-debug-data ${expression_debug_data}"
        }
        if (is_clean_build_validation) {
            extra_args += branch_info.clean_data_validation_pipeline_args ?: ''
        }
        for (value in branch_info.fb_env_values_data) {
            extra_args += " --fb-env-values ${value}"
        }

        if (is_export_data_job) {
            no_trim = false
        } else if (use_super_bundles || branch_info.export_combine_bundles) {
            no_trim = false
        }

        def trim_options = ['True', 'False']
        if (no_trim == true) {
            trim_options = trim_options.reverse()
        }

        String elipy_call = branch_info.elipy_call
        if (is_virtual_stream) {
            String fbenvconfigservice_call = [
                'cli.bat',
                'x64',
                '&&',
                'fb',
                'fbenvconfigservice',
                'config',
                '-o',
                branch_info.data_branch,
                'FB_BRANCH_ID',
            ].join(' ')
            elipy_call = elipy_call.replace('cli.bat x64', fbenvconfigservice_call)
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Builds ' + branch_info.dataset + ' for ' + branch_info.platform + ' with code from ' + branch_info.code_branch)
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned before the build.')
                choiceParam('trim_value', trim_options, 'Should we have trim/emitSuperbundles enabled')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                if (properties_file) {
                    batchFile("TYPE NUL > %TEMP%\\${properties_file}")
                }
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(
                    [
                        elipy_call,
                        'databuild',
                        branch_info.dataset,
                        branch_info.platform,
                        asset,
                        '--code-branch', branch_info.code_branch,
                        '--code-changelist', '%code_changelist%',
                        '--data-branch', branch_info.data_branch,
                        '--data-changelist', '%data_changelist%',
                        '--data-clean', '%clean_data%',
                        '--trim', '%trim_value%',
                    ].join(' ') + ' ' + extra_args
                )
                if (properties_file) {
                    environmentVariables { propertiesFile("\${TEMP}\\" + properties_file) }
                }
            }
        }
    }

    /**
     * Adds generic job parameters for patchdata build jobs.
     */
    static void patchdata_job(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = [branch_info.platform]
        def first_patch = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'first_patch', false)

        def baseline_set = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'baseline_set', true)
        def clear_cache = LibCommonNonCps.get_setting_value(branch_info, ['patchdata', branch_info.platform], 'clear_cache', false)
        def compress_bundles = branch_info.avalanche_compress_bundles ?: false
        def dry_run_patchdata = branch_info.dry_run_patchdata ?: false
        def export_combine_bundles = branch_info.export_combine_bundles ?: false
        def patch_type = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'patch_type', 'incremental')
        def skip_importing_baseline_state = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'skip_importing_baseline_state', false)
        def use_head_bundles_as_base_bundles = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'use_head_bundles_as_base_bundles', false)
        def use_recompression_cache = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'use_recompression_cache', false, project)
        def clean_master_version_check = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'clean_master_version_check', false, project)
        def is_virtual_stream = LibCommonNonCps.get_setting_value(branch_info, [], 'is_virtual_stream', false)
        def standalone_disc_baseline = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'standalone_disc_baseline', false)
        def standalone_patch_baseline = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'standalone_patch_baseline', false)

        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        String poolbuild_label = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'poolbuild_label', 'poolbuild', project)
        // Check for job_label_poolbuild in master settings
        String job_label_poolbuild = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'job_label_poolbuild', null, project)

        if (branch_info.poolbuild_patchdata == true) {
            // Use job_label_poolbuild if defined, otherwise use poolbuild_label
            job_label = job_label_poolbuild ?: poolbuild_label
            job_label += ' && ' + branch_info.platform
        } else if (branch_info.statebuild_patchdata == false) {
            job_label = branch_info.data_branch + ' && patchdata && ' + branch_info.platform
        }

        def timeout_hours = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'timeout_hours_patchdata', 5, project)
        def timeout_minutes = timeout_hours * 60

        def extra_args = branch_info.extra_patchdata_args ?: ''
        if (dry_run_patchdata == true) {
            extra_args += ' --dry-run'
        }

        if (baseline_set == false) {
            extra_args += ' --no-baseline-set'
            patch_type = 'disc_build'
        } else {
            extra_args += ' --disc-code-branch %disc_code_branch% --disc-code-changelist %disc_code_changelist%'
            extra_args += ' --disc-data-branch %disc_data_branch% --disc-data-changelist %disc_data_changelist% '
        }
        if (first_patch == true) {
            extra_args += ' --first-patch'
            if (standalone_disc_baseline) {
                extra_args += ' --standalone-baseline'
            }
        } else {
            extra_args += ' --patch-code-branch %patch_code_branch% --patch-code-changelist %patch_code_changelist%'
            extra_args += ' --patch-data-branch %patch_data_branch% --patch-data-changelist %patch_data_changelist%'
            def non_standalone_patch_baseline = LibCommonNonCps.get_setting_value(branch_info, [], 'non_standalone_patch_baseline', [], project)
            if (standalone_patch_baseline && !(non_standalone_patch_baseline.contains(branch_info.platform))) {
                extra_args += ' --standalone-baseline'
            }
        }

        if (clear_cache) {
            extra_args += ' --clear-cache'
        }
        if (compress_bundles) {
            extra_args += " --enable-compression ${compress_bundles}"
        }
        if (export_combine_bundles) {
            extra_args += ' --export-combine-bundles true'
        }
        if (use_head_bundles_as_base_bundles) {
            extra_args += ' --use-head-bundles-as-base-bundles'
        }
        if (use_recompression_cache) {
            extra_args += " --use-recompression-cache ${use_recompression_cache}"
        }
        if (skip_importing_baseline_state) {
            extra_args += ' --skip-importing-baseline-state'
        }
        if (clean_master_version_check == true) {
            extra_args += " --clean-master-version-check ${clean_master_version_check}"
        }
        if (is_virtual_stream) {
            extra_args += ' --virtual-branch-override true '
        }

        String elipy_call = branch_info.elipy_call
        if (is_virtual_stream) {
            String fbenvconfigservice_call = [
                'cli.bat',
                'x64',
                '&&',
                'fb',
                'fbenvconfigservice',
                'config',
                '-o',
                branch_info.data_branch,
                'FB_BRANCH_ID',
            ].join(' ')
            elipy_call = elipy_call.replace('cli.bat x64', fbenvconfigservice_call)
        }

        //Temporarily stop this since expression debug data is not working when emitting superbundles
        //extra_args += branch_info.expression_debug_data ? ' --expression-debug-data true' : ''

        // Add sections to the Jenkins job.
        job.with {
            description('Builds patches using ' + branch_info.dataset + ' for ' + branch_info.platform + ' with code from ' + branch_info.code_branch + '.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('disc_code_branch')
                    defaultValue('')
                    description('Specifies which branch the disc code came from.')
                    trim(true)
                }
                stringParam {
                    name('disc_code_changelist')
                    defaultValue('')
                    description('Specifies which changelist the disc code was built on.')
                    trim(true)
                }
                stringParam {
                    name('disc_data_branch')
                    defaultValue('')
                    description('Specifies which branch the disc data came from.')
                    trim(true)
                }
                stringParam {
                    name('disc_data_changelist')
                    defaultValue('')
                    description('Specifies which changelist the disc data was built on.')
                    trim(true)
                }
                stringParam {
                    name('patch_code_branch')
                    defaultValue('')
                    description('Specifies which branch the patch code came from.')
                    trim(true)
                }
                stringParam {
                    name('patch_code_changelist')
                    defaultValue('')
                    description('Specifies which changelist the patch code was built on.')
                    trim(true)
                }
                stringParam {
                    name('patch_data_branch')
                    defaultValue('')
                    description('Specifies which branch the patch data came fro.')
                    trim(true)
                }
                stringParam {
                    name('patch_data_changelist')
                    defaultValue('')
                    description('Specifies which changelist the patch data was built on.')
                    trim(true)
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned before the build.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(
                    [
                        elipy_call,
                        'patch_databuild',
                        branch_info.dataset,
                        branch_info.platform,
                        branch_info.asset,
                        '--code-branch', branch_info.code_branch,
                        '--code-changelist', '%code_changelist%',
                        '--data-branch', branch_info.data_branch,
                        '--data-changelist', '%data_changelist%',
                        '--patch-type', patch_type,
                        '--data-clean', '%clean_data%',
                    ].join(' ') + ' ' + extra_args
                )
            }
        }
    }

    /**
     * Add job parameters for a job update p4 counter after data.start job is finished with success.
     */
    static void data_p4counter_updater(def job, def project, def branch_info) {
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        String p4_code_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_server', '', project)
        String p4_data_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_server', '', project)
        // Add sections to the Jenkins job.
        job.with {
            description('P4 counter update after data start job done.')
            label(job_label)
            concurrentBuild()
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(8)
            }
            parameters {
                stringParam {
                    name('code_countername')
                    defaultValue('')
                    description('Specifies p4 code counter name to use.')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist for p4 counter to set value to.')
                    trim(true)
                }
                stringParam {
                    name('data_countername')
                    defaultValue('')
                    description('Specifies p4 data counter name to use.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist for p4 counter to set value to.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}')
                timeout {
                    absolute(100)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' p4_counter' +
                    ' --port ' + p4_code_server +
                    ' --client ' + project.p4_code_client +
                    ' --user ' + project.p4_user_single_slash +
                    ' --countername %code_countername% --value %code_changelist%' +
                    ' --extra-port ' + p4_data_server +
                    ' --extra-client ' + project.p4_data_client +
                    ' --extra-countername %data_countername% --extra-value %data_changelist%')
            }
        }
    }
}
