<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Sync CH1-code-dev code and run Coverity.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>BRANCH_NAME=CH1-code-dev
CODE_BRANCH=CH1-code-dev
CODE_FOLDER=CH1
PROJECT_NAME=bctch1
NON_VIRTUAL_CODE_BRANCH=
NON_VIRTUAL_CODE_FOLDER=</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>CODE_CHANGELIST</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>CLEAN_LOCAL</name>
                    <defaultValue>false</defaultValue>
                    <description>If true, TnT/Local will be deleted at the beginning of the run.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>CLEAN_COVERITY_CLIENT</name>
                    <defaultValue>false</defaultValue>
                    <description>Re-download the Coverity client and regenerate the license.</description>
                </hudson.model.BooleanParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.SCMTrigger>
                    <spec>@daily</spec>
                    <ignorePostCommitHooks>false</ignorePostCommitHooks>
                </hudson.triggers.SCMTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchFile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def branchInfo = branchFile.general_settings + branchFile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * CoverityScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def ignore_paths = branchFile.general_settings?.ignore_paths_code_preview ?: []
                    P4PreviewCode(project, 'stream', env.CODE_FOLDER, env.CODE_BRANCH, env.NON_VIRTUAL_CODE_FOLDER, env.NON_VIRTUAL_CODE_BRANCH, ignore_paths, [], [:])
                }
            }
        }
        stage('Trigger Coverity job') {
            steps {
                script {
                    List&lt;JobReference&gt; jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        def codeChangelist = params.CODE_CHANGELIST ?: env.P4_CHANGELIST
                        def cleanLocal = params.CLEAN_LOCAL
                        def cleanCoverityClient = params.CLEAN_COVERITY_CLIENT

                        def parameters = [
                            string(name: 'CODE_CHANGELIST', value: codeChangelist),
                            booleanParam(name: 'CLEAN_LOCAL', value: cleanLocal),
                            booleanParam(name: 'CLEAN_COVERITY_CLIENT', value: cleanCoverityClient),
                        ]

                        def injectMap = [
                            'CODE_CHANGELIST': codeChangelist,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = "${env.JOB_NAME}.${codeChangelist}"
                        String jobName = "${env.BRANCH_NAME}.coverity"
                        def downstreamJob = build(job: jobName, parameters: parameters, propagate: false)
                        jobReferences &lt;&lt; new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: parameters, propagate: false)

                        def slackSettings = branchInfo.coverity_settings.slack_channel
                        SlackMessageNew(currentBuild, slackSettings, project.short_name)
                    }
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>