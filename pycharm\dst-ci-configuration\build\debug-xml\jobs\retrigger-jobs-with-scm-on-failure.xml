<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Look at not running jobs that implements SCMTriggerItem and see if they are failing with certain strings in their log and then try to trigger them.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>H * * * 1-5</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>50</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

import hudson.model.BuildableItem
import hudson.model.Cause
import hudson.model.Job
import hudson.model.Result
import jenkins.model.Jenkins
import jenkins.triggers.SCMTriggerItem

/**
 * retriggerJobWithScmOnFailure.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Run job to detect stuck jobs failed due to the P4 Polling error') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER
                    List&lt;Job&gt; failedJobs = []
                    List&lt;String&gt; failureStrings = [
                        'Could not read from remote repository',
                        'ERROR: Maximum checkout retry attempts reached, aborting',
                        '*** ERROR: Unable to connect to ldap_host: IO::Socket::INET: Bad hostname \'\' ***',
                        'Unable to resolve Perforce server host name',
                        'Unable to connect to Perforce server',
                    ]
                    echo('Ignoring running jobs...')
                    List&lt;Job&gt; jobs = Jenkins.get().getItems(Job).findAll { job -&gt;
                        job instanceof SCMTriggerItem &amp;&amp; !job.building
                    }

                    echo('Processing jobs...')
                    for (def job : jobs) {
                        try {
                            echo("\tProcessing ${job.name}")
                            def build = job.lastCompletedBuild
                            def buildLog = build?.result == Result.FAILURE ? build.getLog(200) : ['']
                            def failureStringFound = buildLog.any { line -&gt;
                                failureStrings.any { failureString -&gt;
                                    line.contains(failureString)
                                }
                            }

                            if (failureStringFound &amp;&amp; ((SCMTriggerItem) job).SCMTrigger) {
                                failedJobs.add(job)
                            }
                        } catch (FileNotFoundException exc) {
                            echo("No log found for ${job.name}, skipping")
                        }
                    }

                    echo("Found ${failedJobs.size()} failed builds that need retriggering.")
                    failedJobs.each {
                        def triggered = ((BuildableItem) it).scheduleBuild(
                            new Cause.RemoteCause(
                                currentBuild.absoluteUrl,
                                'Triggered jobs after detected failed (usually caused by syncing issues).'
                            )
                        )
                        echo "\t${it.name}: trigger ${triggered ? 'succeeded' : 'failed'}"
                    }

                    if (failedJobs) {
                        currentBuild.result = 'UNSTABLE'
                    }
                }
                SlackMessageNew(currentBuild, '#cobra-support-alerts', 'cob')
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>