<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Scheduler job for data preflighting on kin-stage.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>0</maxConcurrentPerNode>
            <maxConcurrentTotal>4</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>unshelve_changelist</name>
                    <description>Specifies data changelist to preflight</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>preflighter</name>
                    <description>Specifies who triggers the preflight.</description>
                    <defaultValue>anonymous</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>P4CL</name>
                    <description>if enable_custom_changelist is true, changelist(from checkmate) for datapreflight to sync</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>validate_direct_references</name>
                    <defaultValue>false</defaultValue>
                    <description>If true, run the -f validateDirectReferences validation.</description>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>clean_index</name>
                    <defaultValue>false</defaultValue>
                    <description>If true, run the index step with -clean.</description>
                </hudson.model.BooleanParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>branch_name=kin-stage
dataset=kindata
datapreflight_reference_job=kin-stage.data.lastknowngood
project_name=kingston
project_short_name=kin
enable_custom_changelist=true</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

List jobReferences = []

/**
 * data_preflight_scheduler.groovy
 */
pipeline {
    agent { label 'scheduler' }  // we only wanna job to get Running with 'Maximum Total Concurrent Builds', scheduler == master
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger data preflight jobs') {
            steps {
                script {
                    retryOnFailureCause(3, jobReferences) {
                        String unshelveChangelist = params.unshelve_changelist
                        String lkgCodeChangelist = LibJenkins.getLastStableCodeChangelist(env.datapreflight_reference_job)
                        String lkgDataChangelist = LibJenkins.getLastStableDataChangelist(env.datapreflight_reference_job)
                        boolean validateDirectReferences = params.validate_direct_references
                        boolean cleanIndex = params.clean_index

                        def codeChangelist = lkgCodeChangelist ?: ''
                        def dataChangelist = env.enable_custom_changelist?.toBoolean() ? (params.P4CL ?: lkgDataChangelist ?: '') : (lkgDataChangelist ?: '')

                        def args = [
                            string(name: 'code_changelist', value: codeChangelist),
                            string(name: 'data_changelist', value: dataChangelist),
                            string(name: 'unshelve_changelist', value: unshelveChangelist),
                            booleanParam(name: 'validate_direct_references', value: validateDirectReferences),
                            booleanParam(name: 'clean_index', value: cleanIndex),
                        ]
                        def injectMap = [
                            'code_changelist'    : codeChangelist,
                            'data_changelist'    : dataChangelist,
                            'unshelve_changelist': unshelveChangelist,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = "${env.JOB_NAME}.${unshelveChangelist}.${params.preflighter}"

                        echo "Running preflight on code changelist: ${codeChangelist} and data changelist: ${dataChangelist}."

                        def branchFile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
                        def dataPreflightMatrix = branchFile.data_preflight_matrix

                        if (branchFile.preflight_settings.trigger_ado) {
                            LibCommonCps.triggerAdoDataPreflights(this, branchFile, params.preflighter, unshelveChangelist, dataChangelist, codeChangelist)
                        }

                        def jobs = [:]
                        boolean propagate = false

                        dataPreflightMatrix.each { def preflightSet -&gt;
                            String jobName = "${env.branch_name}.${env.dataset}.preflight.${preflightSet.name}"
                            jobs[jobName] = {
                                def downstreamJob = build(job: jobName, parameters: args, propagate: propagate)
                                jobReferences &lt;&lt; new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: args, propagate: propagate)
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                        parallel(jobs)
                    }
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, true) } }
    }
    post {
        always {
            emailext(
                to: '<EMAIL>',
                subject: 'preflight result',
                body: '${SCRIPT, template="email-pipeline-preflight.groovy"}',
                mimeType: 'text/html',
                presendScript: '${SCRIPT, template="preflight-email-presend-pipeline.groovy"}'
            )
        }
        failure {
            sendAlertIfFailedConsecutiveTimes env.JOB_NAME, env.JOB_URL, env.BUILD_NUMBER, '#cobra-red-pod-alerts', env.project_short_name, 5
        }
    }
}

String toString() {
    return 'scripts.schedulers.all.data_preflight_scheduler'
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>