<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>0  22  *  *  0</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>21</daysToKeep>
        <numToKeep>20</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

/**
 * recycleCloudAgents.groovy
 * This will attempt to recycle cloud agents labeled with `managed_cloud_recycled`
 * on a weekly basis, as per EA Security's recommendation.
 */

final String LABEL = 'managed_cloud_recycled'

pipeline {
    agent { label 'master' }
    options {
        timeout(time: 8, unit: 'HOURS')
    }
    stages {
        stage('Recycle Cloud Agents') {
            steps {
                script {
                    List&lt;String&gt; nodesWithLabel = jenkins.model.Jenkins.get().computers
                        .findAll { it.node.labelString.contains(LABEL) }
                        .collect { it.node.nodeName }

                    Map parallelStages = nodesWithLabel.collectEntries {
                        [(it): generateStage(it)]
                    }

                    parallel parallelStages
                }
            }
        }
    }
}

Closure generateStage(nodeName) {
    return {
        stage("Tear down: ${nodeName}") {
            echo "Trying to remove node ${nodeName}"
            def nodeInfo = Jenkins.get().getComputer(nodeName)

            if (nodeInfo.offline) {
                echo 'Node is offline, proceeding to remove'
                nodeInfo.doDoDelete()
            } else {
                node(nodeName) {
                    echo "Removing ${nodeName}"
                    nodeInfo.doDoDelete()
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>