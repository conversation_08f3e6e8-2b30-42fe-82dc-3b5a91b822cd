13:37:20 Traceback (most recent call last):
13:37:20   File "<frozen runpy>", line 198, in _run_module_as_main
13:37:20   File "<frozen runpy>", line 88, in _run_code
13:37:20   File "D:\dev\Python\virtual\Scripts\elipy.exe\__main__.py", line 7, in <module>
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 1130, in __call__
13:37:20     return self.main(*args, **kwargs)
13:37:20            ^^^^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 1055, in main
13:37:20     rv = self.invoke(ctx)
13:37:20          ^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 1657, in invoke
13:37:20     return _process_result(sub_ctx.command.invoke(sub_ctx))
13:37:20                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 1404, in invoke
13:37:20     return ctx.invoke(self.callback, **ctx.params)
13:37:20            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 760, in invoke
13:37:20     return __callback(*args, **kwargs)
13:37:20            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\click\decorators.py", line 84, in new_func
13:37:20     return ctx.invoke(f, obj, *args, **kwargs)
13:37:20            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\click\core.py", line 760, in invoke
13:37:20     return __callback(*args, **kwargs)
13:37:20            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\dice_elipy_scripts\utils\decorators.py", line 68, in wrapper
13:37:20     value = method(*args, **kwargs)
13:37:20             ^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\telemetry.py", line 55, in wrapper
13:37:20     value = method(*args, **kwargs)
13:37:20             ^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\dice_elipy_scripts\frosty.py", line 496, in cli
13:37:20     _filer.fetch_head_bundles(
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\telemetry.py", line 55, in wrapper
13:37:20     value = method(*args, **kwargs)
13:37:20             ^^^^^^^^^^^^^^^^^^^^^^^
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\filer.py", line 412, in fetch_head_bundles
13:37:20     core.robocopy(head_path, dest, purge=True)
13:37:20   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\telemetry.py", line 55, in wrapper
13:37:20     value = method(*args, **kwargs)
13:37:20             ^^^^^^^^^^^^^^^^^^^^^^^
13:37:21   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\core.py", line 467, in robocopy
13:37:21     raise IOError("Source does not exist, cannot run Robocopy: {}".format(source))
13:37:21 OSError: Source does not exist, cannot run Robocopy: \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build\24495830\CH1-content-dev\24495830\xbsx\bundles_combine\head