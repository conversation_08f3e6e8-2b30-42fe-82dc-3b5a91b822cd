<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>H H/12 * * 1-5</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>21</daysToKeep>
        <numToKeep>20</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

import com.cloudbees.groovy.cps.NonCPS

/**
 * deleteAgent.groovy
 * This job is to loop over agents in the current cluster and check when
 * it is offline with reason 'ReadyToBeDeleted' then disconnect it from master
 * This job can be manually triggered or based on a cron timmer
 */

stage('Disconnect Node') {
    node('master') {
        doGetNodeList()
    }
}

@NonCPS
void doGetNodeList() {
    def agentNameList = ''
    for (agent in Jenkins.get().nodes) {
        def computer = agent.computer
        def agentName = agent.computer.name
        if ((computer.offline) &amp;&amp; (computer.offlineCauseReason.contains('[Taint] Tainted'))) {
            echo "we need to disconnect this node ${agentName} from master"
            if (Jenkins.get().getComputer("${agentName}").idle) {
                Jenkins.get().getComputer("${agentName}").doDoDelete()
                echo "${agentName} is disconnected/deleted"
                agentNameList += agentName + ' '
            } else {
                ehco "${agentName} is not idle, wait for next run"
            }
        } else {
            echo "${agentName} is ${computer.offline} offline with reason ${computer.offlineCauseReason}"
        }
    }
    currentBuild.displayName = "${agentNameList}"
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>