"""
<PERSON><PERSON><PERSON> to extract all unique VM labels (platform names) from a Groovy AutotestMatrix file.
This script uses regex to find all Platform(name: ...) occurrences and prints the unique labels.
"""
import re
import sys
from typing import Set

def extract_vm_labels(file_path: str) -> Set[str]:
    """
    Extract all unique VM labels (platform names) from a Groovy AutotestMatrix file.

    Args:
        file_path (str): Path to the Groovy file.

    Returns:
        Set[str]: Set of unique platform names (labels).
    """
    label_pattern = re.compile(r'Platform\(name: Name\.([A-Z0-9_]+)')
    labels = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            for match in label_pattern.findall(line):
                labels.add(match)
    return labels

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python extract_vm_labels.py <AutotestMatrix.groovy>")
        sys.exit(1)
    file_path = sys.argv[1]
    labels = extract_vm_labels(file_path)
    print("Unique VM labels found:")
    for label in sorted(labels):
        print(label)
