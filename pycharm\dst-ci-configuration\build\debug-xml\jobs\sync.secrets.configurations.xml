<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>H/10 * * * 1-5</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <logRotator>
        <daysToKeep>2</daysToKeep>
        <numToKeep>20</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers

/**
 * syncSecretsAndConfigurations.groovy
 * This pipeline will sync secrets and JCasC configuration by cloning
 * container-images/jenkins repo, running the vault sync script and copying
 * jcasc files
 */

pipeline {
    agent { label 'master' }

    options {
        timeout(time: 1, unit: 'HOURS')
    }

    stages {
        stage('Clone container-images/jenkins repo') {
            steps {
                git branch: 'master',
                    credentialsId: 'monkey-commons-ssh-v2',
                    url: '*****************:dre-cobra/container-images/jenkins.git'
            }
        }

        stage('Sync Vault') {
            steps {
                sh 'bash ./bootstrap-config/prepare_vault_jenkins.sh'
            }
        }

        stage('Sync JCasC') {
            steps {
                sh 'bash ./bootstrap-config/copy_jcasc_files.sh'
            }
        }

        stage('Apply Configurations') {
            steps {
                script {
                    if (env.JCASC_RELOAD_TOKEN) {
                        sh '''
                            set +x
                            curl --fail --silent --write-out "Configurations reloaded successfully (HTTP %{http_code})" \
                                 --request POST ${JENKINS_URL}/reload-configuration-as-code/?casc-reload-token=${JCASC_RELOAD_TOKEN}
                            '''
                    } else {
                        echo "JCASC_RELOAD_TOKEN not found, Reload the configuration manually at ${JENKINS_URL}/manage/configuration-as-code/"
                    }
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
</flow-definition>