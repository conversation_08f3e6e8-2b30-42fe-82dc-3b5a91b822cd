<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Runs build deletion using ELIPY2.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>0</maxConcurrentPerNode>
            <maxConcurrentTotal>3</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>H/30 * * * 1-6
H/30 6-23 * * 7</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>project_name=bct
project_with_location=Bct
delete_empty_folders=false</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.dicebuildjenkins

def project = ProjectClass(env.project_name)

/**
 * build_deleter_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger build deleter job') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER
                    def build_deleter_job = build(
                        job: 'utility.build-deleter.' + env.project_with_location,
                        parameters: env.delete_empty_folders.toBoolean() ? [string(name: 'delete_empty_folders', value: '--empty-folders'), string(name: 'timeout', value: (60 * 24).toString())] : null,
                        propagate: false
                    )
                    currentBuild.result = build_deleter_job.result.toString()

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
    post { always { SlackMessageNew(currentBuild, '#cobra-outage-builddeleter', project.short_name) } }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>